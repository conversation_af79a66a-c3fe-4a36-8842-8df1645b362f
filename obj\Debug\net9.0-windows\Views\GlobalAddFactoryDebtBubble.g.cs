﻿#pragma checksum "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7006CB28940DC37CDA60EA8FEA30F9898FAFC38C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// GlobalAddFactoryDebtBubble
    /// </summary>
    public partial class GlobalAddFactoryDebtBubble : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 75 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PersonSelectionLabel;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PersonComboBox;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearPersonSelectionButton;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DebtDatePicker;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InvoiceTypeTextBoxBorder;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceTypeTextBox;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SuggestionsPopup;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SuggestionsListBox;
        
        #line default
        #line hidden
        
        
        #line 503 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 539 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 554 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PaidAmountPanel;
        
        #line default
        #line hidden
        
        
        #line 560 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PaidAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 572 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RemainingAmountPanel;
        
        #line default
        #line hidden
        
        
        #line 583 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 614 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachmentPathTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/globaladdfactorydebtbubble.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PersonSelectionLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.PersonComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.ClearPersonSelectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.ClearPersonSelectionButton.Click += new System.Windows.RoutedEventHandler(this.ClearPersonSelectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 231 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.QuantityTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 6:
            this.UnitPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 250 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.UnitPriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.DebtDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.InvoiceTypeTextBoxBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.InvoiceTypeTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 333 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.InvoiceTypeTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.InvoiceTypeTextBox_TextChanged);
            
            #line default
            #line hidden
            
            #line 334 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.InvoiceTypeTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.InvoiceTypeTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 335 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.InvoiceTypeTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.InvoiceTypeTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 336 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.InvoiceTypeTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.InvoiceTypeTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SuggestionsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 12:
            this.SuggestionsListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 402 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.SuggestionsListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SuggestionsListBox_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 403 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.SuggestionsListBox.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.SuggestionsListBox_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 546 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PaidAmountPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.PaidAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 568 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            this.PaidAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PaidAmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.RemainingAmountPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 18:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.AttachmentPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            
            #line 627 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 649 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 662 "..\..\..\..\Views\GlobalAddFactoryDebtBubble.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

