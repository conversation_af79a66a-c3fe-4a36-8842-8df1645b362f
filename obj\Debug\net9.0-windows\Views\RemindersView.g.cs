﻿#pragma checksum "..\..\..\..\Views\RemindersView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7CF71E398CF2E651EF0F68EA81C0BB4C073E2473"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// RemindersView
    /// </summary>
    public partial class RemindersView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 68 "..\..\..\..\Views\RemindersView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RemindersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\RemindersView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalRemindersCount;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Views\RemindersView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayRemindersCount;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\RemindersView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueRemindersCount;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Views\RemindersView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedRemindersCount;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/remindersview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\RemindersView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 30 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddReminder_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 35 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TodayReminders_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 41 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OverdueReminders_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 46 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ReminderSettings_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RemindersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 6:
            
            #line 80 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.MarkCompleted_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 83 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditReminder_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 87 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteReminder_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TotalRemindersCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TodayRemindersCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.OverdueRemindersCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.CompletedRemindersCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 184 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CompleteReminder_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 189 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditReminder_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 196 "..\..\..\..\Views\RemindersView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteReminder_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

