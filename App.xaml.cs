﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PdfSharp.Fonts;

namespace DebtManagementApp
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // إعداد معالج الأخطاء العامة
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                DispatcherUnhandledException += OnDispatcherUnhandledException;

                // تهيئة Font Resolver لـ PDF
                try
                {
                    if (GlobalFontSettings.FontResolver == null)
                    {
                        GlobalFontSettings.FontResolver = new Helpers.PdfFontResolver();
                    }
                }
                catch (Exception fontEx)
                {
                    System.Diagnostics.Debug.WriteLine($"تحذير: فشل في تهيئة Font Resolver: {fontEx.Message}");
                }

                // تهيئة قاعدة البيانات باستخدام DatabaseHelper
                try
                {
                    DatabaseHelper.InitializeDatabase();
                }
                catch (Exception dbEx)
                {
                    MessageBox.Show($"خطأ في قاعدة البيانات: {dbEx.Message}", "خطأ قاعدة البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تهيئة التطبيق:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ فادح",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            MessageBox.Show($"خطأ في التطبيق:\n{e.Exception.Message}\n\nStack Trace:\n{e.Exception.StackTrace}",
                "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            MessageBox.Show($"خطأ فادح:\n{exception?.Message}\n\nStack Trace:\n{exception?.StackTrace}",
                "خطأ فادح", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            base.OnExit(e);
        }

        // دالة تحسين التمرير بالماوس مع السلاسة
        public static void ScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (sender is ScrollViewer scrollViewer)
            {
                // تحسين سرعة التمرير مع السلاسة
                double scrollSpeed = 2.0; // سرعة معتدلة للسلاسة
                double targetOffset = scrollViewer.VerticalOffset - (e.Delta * scrollSpeed);

                // التأكد من أن القيمة ضمن الحدود المسموحة
                targetOffset = Math.Max(0, Math.Min(scrollViewer.ScrollableHeight, targetOffset));

                // تطبيق التمرير السلس
                SmoothScrollToOffset(scrollViewer, targetOffset);

                // منع التمرير الافتراضي
                e.Handled = true;
            }
        }

        // دالة التمرير السلس باستخدام Timer
        private static void SmoothScrollToOffset(ScrollViewer scrollViewer, double targetOffset)
        {
            double startOffset = scrollViewer.VerticalOffset;
            double distance = targetOffset - startOffset;

            if (Math.Abs(distance) < 1) return; // إذا كانت المسافة صغيرة جداً، لا حاجة للحركة

            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromMilliseconds(16); // 60 FPS

            DateTime startTime = DateTime.Now;
            double duration = 150; // مدة الحركة بالميلي ثانية

            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // استخدام دالة Ease Out للسلاسة
                double easedProgress = 1 - Math.Pow(1 - progress, 3);

                double currentOffset = startOffset + (distance * easedProgress);
                scrollViewer.ScrollToVerticalOffset(currentOffset);

                if (progress >= 1.0)
                {
                    timer.Stop();
                }
            };

            timer.Start();
        }
    }
}

