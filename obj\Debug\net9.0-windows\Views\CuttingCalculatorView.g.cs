﻿#pragma checksum "..\..\..\..\Views\CuttingCalculatorView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CA581DB39E350CBA6D38E1F627CE5F1AC010E7A0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// CuttingCalculatorView
    /// </summary>
    public partial class CuttingCalculatorView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 49 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CutDistanceMmTextBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PricePerMeterTextBox;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IronThicknessTextBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAreaText;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCutLengthText;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CuttingCostText;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCostText;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaterialInfoText;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CutTypeInfoText;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeEstimateText;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\CuttingCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CalculationDateText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/cuttingcalculatorview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 4 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            ((DebtManagementApp.Views.CuttingCalculatorView)(target)).Loaded += new System.Windows.RoutedEventHandler(this.CuttingCalculatorView_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 27 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Reset_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 30 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveResult_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CutDistanceMmTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 50 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            this.CutDistanceMmTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PricePerMeterTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 56 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            this.PricePerMeterTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.IronThicknessTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 62 "..\..\..\..\Views\CuttingCalculatorView.xaml"
            this.IronThicknessTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TotalAreaText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TotalCutLengthText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CuttingCostText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalCostText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.DetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.MaterialInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CutTypeInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TimeEstimateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.CalculationDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

