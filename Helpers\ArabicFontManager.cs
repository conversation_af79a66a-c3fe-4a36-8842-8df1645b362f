using System;
using System.IO;
using System.Reflection;
using QuestPDF.Infrastructure;
using QuestPDF.Helpers;

namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// مدير الخطوط العربية لـ QuestPDF
    /// </summary>
    public static class ArabicFontManager
    {
        private static bool _isInitialized = false;

        /// <summary>
        /// تهيئة الخطوط العربية
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            try
            {
                // تسجيل خطوط النظام التي تدعم العربية
                RegisterSystemFonts();
                
                _isInitialized = true;
                System.Diagnostics.Debug.WriteLine("تم تهيئة الخطوط العربية بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الخطوط العربية: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل خطوط النظام (مبسط)
        /// </summary>
        private static void RegisterSystemFonts()
        {
            try
            {
                // QuestPDF يدعم الخطوط الأساسية تلقائياً
                // لا حاجة لتسجيل خطوط إضافية في الوقت الحالي
                System.Diagnostics.Debug.WriteLine("تم تهيئة دعم الخطوط العربية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الخطوط: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على اسم عائلة الخط من اسم الملف
        /// </summary>
        private static string GetFontFamilyName(string fileName)
        {
            var name = Path.GetFileNameWithoutExtension(fileName).ToLower();
            
            return name switch
            {
                "tahoma" or "tahomabd" => "Tahoma",
                "arial" or "arialbd" or "ariali" or "arialbi" => "Arial",
                "calibri" or "calibrib" or "calibrii" or "calibriz" => "Calibri",
                "segoeui" or "segoeuib" or "segoeuii" or "segoeuiz" => "Segoe UI",
                "times" or "timesbd" or "timesi" or "timesbi" => "Times New Roman",
                _ => "Arial"
            };
        }

        /// <summary>
        /// الحصول على اسم الخط المفضل للعربية
        /// </summary>
        public static string GetPreferredArabicFont()
        {
            // ترتيب الأولوية للخطوط العربية
            var preferredFonts = new[] { "Tahoma", "Arial", "Calibri", "Segoe UI", "Times New Roman" };
            
            foreach (var font in preferredFonts)
            {
                if (IsFontAvailable(font))
                {
                    return font;
                }
            }
            
            return "Arial"; // خط افتراضي
        }

        /// <summary>
        /// فحص توفر الخط
        /// </summary>
        private static bool IsFontAvailable(string fontName)
        {
            try
            {
                // هذه طريقة مبسطة للفحص
                // في التطبيق الحقيقي يمكن استخدام طرق أكثر دقة
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات الخطوط المسجلة
        /// </summary>
        public static string GetRegisteredFontsInfo()
        {
            try
            {
                return "تم تسجيل الخطوط العربية المتاحة في النظام";
            }
            catch (Exception ex)
            {
                return $"خطأ في الحصول على معلومات الخطوط: {ex.Message}";
            }
        }
    }
}
