﻿#pragma checksum "..\..\..\..\Views\AddFactoryDebtWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A8EBE31558C0E4F5C4A8705C749232B713829E90"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// AddFactoryDebtWindow
    /// </summary>
    public partial class AddFactoryDebtWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 92 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PersonComboBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DebtDatePicker;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InvoiceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label PaidAmountLabel;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PaidAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label RemainingAmountLabel;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AttachmentPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseAttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/addfactorydebtwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PersonComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 100 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.QuantityTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 3:
            this.UnitPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 106 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.UnitPriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CalculateTotal);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.DebtDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.InvoiceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.PaymentStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 144 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.PaymentStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PaidAmountLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 10:
            this.PaidAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 157 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.PaidAmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PaidAmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.RemainingAmountLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 12:
            this.RemainingAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.AttachmentPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.BrowseAttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 188 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.BrowseAttachmentButton.Click += new System.Windows.RoutedEventHandler(this.BrowseAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 204 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 210 "..\..\..\..\Views\AddFactoryDebtWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

