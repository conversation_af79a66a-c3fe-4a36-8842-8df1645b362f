﻿#pragma checksum "..\..\..\..\Views\SettingsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "67520D710858B511148BCD27919AA7EE8D72F909"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// SettingsView
    /// </summary>
    public partial class SettingsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 38 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyLogoPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestLogoButton;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintTestLogoButton;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LogoPreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image LogoPreviewImage;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DefaultCurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultGracePeriodTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LateInterestRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoReminderCheckBox;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReminderDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinimumDebtAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultIronPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultCuttingCostTextBox;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TransportationRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultWeldingCostTextBox;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DefaultProfitMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VATRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartWithSystemCheckBox;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoUpdatesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DesktopNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SoundAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetColumnSizesButton;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestSaveButton;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CreateVersionedBackupsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 432 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoDeleteOldBackupsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\..\..\Views\SettingsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MaxBackupVersionsSlider;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/settingsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SettingsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CompanyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CompanyAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CompanyPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CompanyEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CompanyLogoPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            
            #line 82 "..\..\..\..\Views\SettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseCompanyLogo_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 87 "..\..\..\..\Views\SettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveCompanyLogo_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TestLogoButton = ((System.Windows.Controls.Button)(target));
            
            #line 98 "..\..\..\..\Views\SettingsView.xaml"
            this.TestLogoButton.Click += new System.Windows.RoutedEventHandler(this.TestLogo_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PrintTestLogoButton = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\Views\SettingsView.xaml"
            this.PrintTestLogoButton.Click += new System.Windows.RoutedEventHandler(this.PrintTestLogo_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.LogoPreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.LogoPreviewImage = ((System.Windows.Controls.Image)(target));
            return;
            case 12:
            this.DefaultCurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.DefaultGracePeriodTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.LateInterestRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.AutoReminderCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.ReminderDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.MinimumDebtAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.AutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.DefaultIronPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.DefaultCuttingCostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            this.TransportationRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.DefaultWeldingCostTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.DefaultProfitMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.VATRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            
            #line 306 "..\..\..\..\Views\SettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseBackupPath_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.StartWithSystemCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 29:
            this.AutoUpdatesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.LogLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 31:
            this.DesktopNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 32:
            this.SoundAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.ResetColumnSizesButton = ((System.Windows.Controls.Button)(target));
            
            #line 374 "..\..\..\..\Views\SettingsView.xaml"
            this.ResetColumnSizesButton.Click += new System.Windows.RoutedEventHandler(this.ResetColumnSizes_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.TestSaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 382 "..\..\..\..\Views\SettingsView.xaml"
            this.TestSaveButton.Click += new System.Windows.RoutedEventHandler(this.TestSave_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.CreateVersionedBackupsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 36:
            this.AutoDeleteOldBackupsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 37:
            this.MaxBackupVersionsSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 38:
            
            #line 502 "..\..\..\..\Views\SettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetSettings_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            
            #line 507 "..\..\..\..\Views\SettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelSettings_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            
            #line 512 "..\..\..\..\Views\SettingsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettings_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

