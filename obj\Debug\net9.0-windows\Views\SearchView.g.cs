﻿#pragma checksum "..\..\..\..\Views\SearchView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7CB44F86E0E9F24DC30E5D5A7F70DC040DAAFEE7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// SearchView
    /// </summary>
    public partial class SearchView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 39 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SearchTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DebtStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DateRangeComboBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AmountRangeComboBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultsCountText;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl ResultsTabControl;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem PersonsTab;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PersonsResultsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem DebtsTab;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DebtsResultsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem RemindersTab;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\SearchView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RemindersResultsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/searchview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SearchView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 42 "..\..\..\..\Views\SearchView.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 47 "..\..\..\..\Views\SearchView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Search_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 64 "..\..\..\..\Views\SearchView.xaml"
            this.SearchTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SearchTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DebtStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 77 "..\..\..\..\Views\SearchView.xaml"
            this.DebtStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DebtStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DateRangeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 91 "..\..\..\..\Views\SearchView.xaml"
            this.DateRangeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DateRangeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AmountRangeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 106 "..\..\..\..\Views\SearchView.xaml"
            this.AmountRangeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AmountRangeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ResultsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            
            #line 136 "..\..\..\..\Views\SearchView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetFilters_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 141 "..\..\..\..\Views\SearchView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportResults_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 146 "..\..\..\..\Views\SearchView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSearch_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ResultsTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 12:
            this.PersonsTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 13:
            this.PersonsResultsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.DebtsTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 15:
            this.DebtsResultsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            this.RemindersTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 17:
            this.RemindersResultsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

