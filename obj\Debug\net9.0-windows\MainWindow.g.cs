﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "38E6C4EF68E6551634B09C8A43A950C645ECCE8D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DebtManagementApp;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 58 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NotificationsButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotificationBadge;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotificationCount;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeDisplay;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer SidebarScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NavigationButtons;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDashboard;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPersons;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnWorkers;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDebts;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFactoryDebts;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOverdue;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReports;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBackup;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnIronCalc;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCuttingCalc;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReminders;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearch;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSettings;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnNetwork;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOneDrive;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnTestBubble;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContentTitle;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MainContentScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl MainContent;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPersonsCount;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDebtsCount;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueDebtsCount;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountDisplay;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView RecentActivitiesList;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu ActivityContextMenu;
        
        #line default
        #line hidden
        
        
        #line 463 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GlobalOverlay;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl GlobalBubbleContent;
        
        #line default
        #line hidden
        
        
        #line 494 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas NotificationContainer;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.NotificationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\MainWindow.xaml"
            this.NotificationsButton.Click += new System.Windows.RoutedEventHandler(this.NotificationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.NotificationBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.NotificationCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TimeDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.SidebarScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            
            #line 124 "..\..\..\MainWindow.xaml"
            this.SidebarScrollViewer.PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            return;
            case 6:
            this.NavigationButtons = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.BtnDashboard = ((System.Windows.Controls.Button)(target));
            
            #line 139 "..\..\..\MainWindow.xaml"
            this.BtnDashboard.Click += new System.Windows.RoutedEventHandler(this.ReturnToDashboard_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnPersons = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\MainWindow.xaml"
            this.BtnPersons.Click += new System.Windows.RoutedEventHandler(this.NavigateToPersons);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnWorkers = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\MainWindow.xaml"
            this.BtnWorkers.Click += new System.Windows.RoutedEventHandler(this.NavigateToWorkers);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnDebts = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\MainWindow.xaml"
            this.BtnDebts.Click += new System.Windows.RoutedEventHandler(this.NavigateToDebts);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnFactoryDebts = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\MainWindow.xaml"
            this.BtnFactoryDebts.Click += new System.Windows.RoutedEventHandler(this.NavigateToFactoryDebts);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnOverdue = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\MainWindow.xaml"
            this.BtnOverdue.Click += new System.Windows.RoutedEventHandler(this.NavigateToOverdue);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnReports = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\MainWindow.xaml"
            this.BtnReports.Click += new System.Windows.RoutedEventHandler(this.NavigateToReports);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnBackup = ((System.Windows.Controls.Button)(target));
            
            #line 181 "..\..\..\MainWindow.xaml"
            this.BtnBackup.Click += new System.Windows.RoutedEventHandler(this.NavigateToBackup);
            
            #line default
            #line hidden
            return;
            case 15:
            this.BtnIronCalc = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\MainWindow.xaml"
            this.BtnIronCalc.Click += new System.Windows.RoutedEventHandler(this.NavigateToIronCalc);
            
            #line default
            #line hidden
            return;
            case 16:
            this.BtnCuttingCalc = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\MainWindow.xaml"
            this.BtnCuttingCalc.Click += new System.Windows.RoutedEventHandler(this.NavigateToCuttingCalc);
            
            #line default
            #line hidden
            return;
            case 17:
            this.BtnReminders = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\MainWindow.xaml"
            this.BtnReminders.Click += new System.Windows.RoutedEventHandler(this.NavigateToReminders);
            
            #line default
            #line hidden
            return;
            case 18:
            this.BtnSearch = ((System.Windows.Controls.Button)(target));
            
            #line 211 "..\..\..\MainWindow.xaml"
            this.BtnSearch.Click += new System.Windows.RoutedEventHandler(this.NavigateToSearch);
            
            #line default
            #line hidden
            return;
            case 19:
            this.BtnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 216 "..\..\..\MainWindow.xaml"
            this.BtnSettings.Click += new System.Windows.RoutedEventHandler(this.NavigateToSettings);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BtnNetwork = ((System.Windows.Controls.Button)(target));
            
            #line 221 "..\..\..\MainWindow.xaml"
            this.BtnNetwork.Click += new System.Windows.RoutedEventHandler(this.NavigateToNetwork);
            
            #line default
            #line hidden
            return;
            case 21:
            this.BtnOneDrive = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\MainWindow.xaml"
            this.BtnOneDrive.Click += new System.Windows.RoutedEventHandler(this.NavigateToOneDrive);
            
            #line default
            #line hidden
            return;
            case 22:
            this.BtnTestBubble = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\MainWindow.xaml"
            this.BtnTestBubble.Click += new System.Windows.RoutedEventHandler(this.TestGlobalBubble_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ContentTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            
            #line 262 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshContent);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 264 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportContent);
            
            #line default
            #line hidden
            return;
            case 26:
            this.MainContentScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            
            #line 274 "..\..\..\MainWindow.xaml"
            this.MainContentScrollViewer.PreviewMouseWheel += new System.Windows.Input.MouseWheelEventHandler(this.ScrollViewer_PreviewMouseWheel);
            
            #line default
            #line hidden
            return;
            case 27:
            this.MainContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            case 28:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 29:
            this.TotalPersonsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.TotalDebtsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.OverdueDebtsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.TotalAmountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.RecentActivitiesList = ((System.Windows.Controls.ListView)(target));
            return;
            case 34:
            this.ActivityContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            return;
            case 35:
            
            #line 346 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteActivity_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            
            #line 354 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.UndoActivity_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            
            #line 397 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAddPerson);
            
            #line default
            #line hidden
            return;
            case 38:
            
            #line 400 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAddDebt);
            
            #line default
            #line hidden
            return;
            case 39:
            
            #line 416 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickReport);
            
            #line default
            #line hidden
            return;
            case 40:
            
            #line 432 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestUndo);
            
            #line default
            #line hidden
            return;
            case 41:
            this.GlobalOverlay = ((System.Windows.Controls.Grid)(target));
            
            #line 467 "..\..\..\MainWindow.xaml"
            this.GlobalOverlay.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.GlobalOverlay_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 42:
            this.GlobalBubbleContent = ((System.Windows.Controls.ContentControl)(target));
            
            #line 490 "..\..\..\MainWindow.xaml"
            this.GlobalBubbleContent.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.GlobalBubbleContent_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 43:
            this.NotificationContainer = ((System.Windows.Controls.Canvas)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

