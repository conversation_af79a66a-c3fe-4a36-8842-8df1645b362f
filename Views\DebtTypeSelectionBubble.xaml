<UserControl x:Class="DebtManagementApp.Views.DebtTypeSelectionBubble"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- فقاعة اختيار نوع الدين مع تأثير البلور -->
    <Border Width="600"
            Height="450"
            Margin="15"
            CornerRadius="20"
            BorderThickness="1">

        <!-- خلفية زجاجية متدرجة -->
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#F8FFFFFF" Offset="0"/>
                <GradientStop Color="#F0FFFFFF" Offset="0.5"/>
                <GradientStop Color="#E8FFFFFF" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>

        <!-- حدود زجاجية -->
        <Border.BorderBrush>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#40FFFFFF" Offset="0"/>
                <GradientStop Color="#20FFFFFF" Offset="0.5"/>
                <GradientStop Color="#10FFFFFF" Offset="1"/>
            </LinearGradientBrush>
        </Border.BorderBrush>

        <!-- تأثير الظل والبلور المتقدم -->
        <Border.Effect>
            <DropShadowEffect Color="#40000000" 
                            BlurRadius="25" 
                            ShadowDepth="8" 
                            Direction="270" 
                            Opacity="0.3"/>
        </Border.Effect>

        <!-- المحتوى الرئيسي -->
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,40">
                <TextBlock Text="💰" FontSize="32" Margin="0,0,15,0"/>
                <TextBlock Text="اختر نوع الدين"
                          FontSize="24"
                          FontWeight="Bold"
                          Foreground="#2C3E50"
                          VerticalAlignment="Center"/>
            </StackPanel>

            <!-- الأزرار -->
            <StackPanel Grid.Row="1" Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">

                <!-- زر إضافة دين شخص -->
                <Button x:Name="AddPersonDebtButton"
                        Content="👤 إضافة دين شخص"
                        Style="{StaticResource InfoButton}"
                        Padding="35,18"
                        Margin="0,10,0,25"
                        FontSize="18"
                        FontWeight="SemiBold"
                        MinWidth="320"
                        Height="65"
                        Click="AddPersonDebtButton_Click"/>

                <!-- زر إضافة دين معمل -->
                <Button x:Name="AddFactoryDebtButton"
                        Content="🏭 إضافة دين معمل"
                        Style="{StaticResource WarningButton}"
                        Padding="35,18"
                        Margin="0,0,0,10"
                        FontSize="18"
                        FontWeight="SemiBold"
                        MinWidth="320"
                        Height="65"
                        Click="AddFactoryDebtButton_Click"/>

            </StackPanel>

            <!-- زر الإغلاق -->
            <Button Grid.Row="2"
                    x:Name="CloseButton"
                    Content="✖️ إغلاق"
                    Style="{StaticResource SecondaryButton}"
                    Padding="40,15"
                    Margin="0,40,0,20"
                    FontSize="18"
                    MinWidth="150"
                    Height="55"
                    HorizontalAlignment="Center"
                    Click="CloseButton_Click"/>

        </Grid>
    </Border>
</UserControl>
