﻿#pragma checksum "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E0509105FCC29B0BF913D599858BD4F6B05772B6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// PersonFactoryInvoicesControl
    /// </summary>
    public partial class PersonFactoryInvoicesControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 71 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackButton;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PersonNameTitle;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDebtText;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidDebtText;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceCountText;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddInvoiceButton;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportInvoicesButton;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu InvoicesContextMenu;
        
        #line default
        #line hidden
        
        
        #line 569 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 571 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidInvoicesText;
        
        #line default
        #line hidden
        
        
        #line 573 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnpaidInvoicesCountText;
        
        #line default
        #line hidden
        
        
        #line 576 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/personfactoryinvoicescontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BackButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            this.BackButton.Click += new System.Windows.RoutedEventHandler(this.BackButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PersonNameTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TotalDebtText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.PaidDebtText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.InvoiceCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.UnpaidInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AddInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            this.AddInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.AddInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportInvoicesButton = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            this.ExportInvoicesButton.Click += new System.Windows.RoutedEventHandler(this.ExportInvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 186 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            this.InvoicesDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.InvoicesDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 10:
            this.InvoicesContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            return;
            case 11:
            
            #line 190 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.EditInvoiceMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 191 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteInvoiceMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 193 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.MarkAsPaidMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 194 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.MarkAsUnpaidMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 195 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.PartialPaymentMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 198 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDetailsMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 199 "..\..\..\..\Views\PersonFactoryInvoicesControl.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewAttachmentMenuItem_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TotalInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PaidInvoicesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.UnpaidInvoicesCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

