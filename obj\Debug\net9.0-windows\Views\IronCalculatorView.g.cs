﻿#pragma checksum "..\..\..\..\Views\IronCalculatorView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "98172C026B340A4CF83098DAB5E19F1EEB70A369"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// IronCalculatorView
    /// </summary>
    public partial class IronCalculatorView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 48 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TonPriceUsdTextBox;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsdToIqdRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LengthTextBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WidthTextBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ThicknessTextBox;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TransportCostTextBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BasicCostText;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TransportCostText;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxCostText;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AdditionalFeesText;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCostText;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeightPerMeterText;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalWeightText;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PricePerKgText;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\IronCalculatorView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CalculationDateText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/ironcalculatorview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\IronCalculatorView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 4 "..\..\..\..\Views\IronCalculatorView.xaml"
            ((DebtManagementApp.Views.IronCalculatorView)(target)).Loaded += new System.Windows.RoutedEventHandler(this.IronCalculatorView_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 27 "..\..\..\..\Views\IronCalculatorView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Reset_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 30 "..\..\..\..\Views\IronCalculatorView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveResult_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TonPriceUsdTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 48 "..\..\..\..\Views\IronCalculatorView.xaml"
            this.TonPriceUsdTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Input_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.UsdToIqdRateTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 53 "..\..\..\..\Views\IronCalculatorView.xaml"
            this.UsdToIqdRateTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Input_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.LengthTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 58 "..\..\..\..\Views\IronCalculatorView.xaml"
            this.LengthTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Input_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.WidthTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 63 "..\..\..\..\Views\IronCalculatorView.xaml"
            this.WidthTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Input_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ThicknessTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 68 "..\..\..\..\Views\IronCalculatorView.xaml"
            this.ThicknessTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Input_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TransportCostTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 73 "..\..\..\..\Views\IronCalculatorView.xaml"
            this.TransportCostTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Input_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BasicCostText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TransportCostText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TaxCostText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.AdditionalFeesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TotalCostText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.DetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.WeightPerMeterText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TotalWeightText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.PricePerKgText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.CalculationDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

