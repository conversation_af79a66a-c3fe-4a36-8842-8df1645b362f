using System;
using System.Windows;
using System.Windows.Controls;
using DebtManagementApp.Models;

namespace DebtManagementApp.Views
{
    /// <summary>
    /// فقاعة اختيار نوع الدين
    /// </summary>
    public partial class DebtTypeSelectionBubble : UserControl
    {
        private Person? _person;

        /// <summary>
        /// حدث طلب إضافة دين شخص
        /// </summary>
        public event EventHandler<PersonEventArgs>? AddPersonDebtRequested;

        /// <summary>
        /// حدث طلب إضافة دين معمل
        /// </summary>
        public event EventHandler<PersonEventArgs>? AddFactoryDebtRequested;

        /// <summary>
        /// حدث طلب الإغلاق
        /// </summary>
        public event EventHandler? CloseRequested;

        public DebtTypeSelectionBubble()
        {
            InitializeComponent();
        }

        /// <summary>
        /// تعيين بيانات الشخص
        /// </summary>
        public void SetPerson(Person person)
        {
            _person = person ?? throw new ArgumentNullException(nameof(person));
        }

        private void AddPersonDebtButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_person != null)
                {
                    AddPersonDebtRequested?.Invoke(this, new PersonEventArgs(_person));
                }
                else
                {
                    MessageBox.Show("الشخص غير محدد!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طلب إضافة دين الشخص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddFactoryDebtButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_person != null)
                {
                    AddFactoryDebtRequested?.Invoke(this, new PersonEventArgs(_person));
                }
                else
                {
                    MessageBox.Show("الشخص غير محدد!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طلب إضافة دين المعمل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CloseRequested?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق الفقاعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
