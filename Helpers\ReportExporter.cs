using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using DebtManagementApp.Models;
using Microsoft.Win32;
using PdfSharp.Pdf;
using PdfSharp.Drawing;
using System.Drawing;
using System.Drawing.Imaging;


namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// مساعد تصدير التقارير إلى تنسيقات مختلفة
    /// </summary>
    public static class ReportExporter
    {
        /// <summary>
        /// تصدير التقرير إلى ملف PDF (محاكاة - سيتم تطوير PDF لاحقاً)
        /// </summary>
        public static bool ExportToPdf(ReportData report, List<Debt> debts, string? filePath = null)
        {
            try
            {
                if (filePath == null)
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "PDF files (*.pdf)|*.pdf|All files (*.*)|*.*",
                        DefaultExt = "pdf",
                        FileName = $"تقرير_{report.ReportType}_{DateTime.Now:yyyyMMdd}.pdf"
                    };

                    if (saveDialog.ShowDialog() != true)
                        return false;

                    filePath = saveDialog.FileName;
                }

                // محاكاة تصدير PDF - سيتم استبدالها بمكتبة PDF حقيقية
                var htmlContent = GenerateHtmlReport(report, debts);
                var pdfContent = ConvertHtmlToPdfSimulation(htmlContent);
                
                File.WriteAllText(filePath, pdfContent, Encoding.UTF8);

                MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "نجح التصدير",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير التقرير إلى ملف Excel (CSV)
        /// </summary>
        public static bool ExportToExcel(ReportData report, List<Debt> debts, string? filePath = null)
        {
            try
            {
                if (filePath == null)
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "Excel files (*.csv)|*.csv|All files (*.*)|*.*",
                        DefaultExt = "csv",
                        FileName = $"تقرير_{report.ReportType}_{DateTime.Now:yyyyMMdd}.csv"
                    };

                    if (saveDialog.ShowDialog() != true)
                        return false;

                    filePath = saveDialog.FileName;
                }

                var csvContent = GenerateCsvReport(report, debts);
                File.WriteAllText(filePath, csvContent, Encoding.UTF8);

                MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "نجح التصدير",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير التقرير إلى ملف نصي
        /// </summary>
        public static bool ExportToText(ReportData report, List<Debt> debts, string? filePath = null)
        {
            try
            {
                if (filePath == null)
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*",
                        DefaultExt = "txt",
                        FileName = $"تقرير_{report.ReportType}_{DateTime.Now:yyyyMMdd}.txt"
                    };

                    if (saveDialog.ShowDialog() != true)
                        return false;

                    filePath = saveDialog.FileName;
                }

                var textContent = GenerateTextReport(report, debts);
                File.WriteAllText(filePath, textContent, Encoding.UTF8);

                MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}", "نجح التصدير",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير النص:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء تقرير HTML
        /// </summary>
        private static string GenerateHtmlReport(ReportData report, List<Debt> debts)
        {
            var html = new StringBuilder();
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<title>تقرير إدارة الديون</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; }");
            html.AppendLine("h1 { color: #1565C0; text-align: center; }");
            html.AppendLine("h2 { color: #1976D2; border-bottom: 2px solid #E0E0E0; padding-bottom: 5px; }");
            html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #E0E0E0; padding: 8px; text-align: center; }");
            html.AppendLine("th { background-color: #1565C0; color: white; }");
            html.AppendLine("tr:nth-child(even) { background-color: #F8F9FA; }");
            html.AppendLine(".summary { background-color: #E3F2FD; padding: 15px; border-radius: 8px; margin: 20px 0; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // العنوان الرئيسي
            html.AppendLine($"<h1>تقرير {report.ReportType}</h1>");
            html.AppendLine($"<p style='text-align: center; color: #666;'>الفترة: {report.FormattedDateRange}</p>");

            // ملخص الإحصائيات
            html.AppendLine("<div class='summary'>");
            html.AppendLine("<h2>ملخص الإحصائيات</h2>");
            html.AppendLine($"<p><strong>إجمالي المعاملات:</strong> {report.TotalTransactions}</p>");
            html.AppendLine($"<p><strong>المعاملات المسددة:</strong> {report.SettledTransactions} ({report.FormattedSettlementRate})</p>");
            html.AppendLine($"<p><strong>المعاملات غير المسددة:</strong> {report.UnsettledTransactions}</p>");
            html.AppendLine($"<p><strong>المعاملات المتأخرة:</strong> {report.OverdueTransactions}</p>");
            html.AppendLine($"<p><strong>الإجمالي العام:</strong> {report.FormattedGrandTotal}</p>");
            html.AppendLine("</div>");

            // تفاصيل التكاليف
            html.AppendLine("<h2>تفاصيل التكاليف</h2>");
            html.AppendLine("<table>");
            html.AppendLine("<tr><th>نوع التكلفة</th><th>المبلغ</th></tr>");
            html.AppendLine($"<tr><td>تكاليف القطع</td><td>{report.FormattedTotalCuttingCost}</td></tr>");
            html.AppendLine($"<tr><td>تكاليف الحديد</td><td>{report.FormattedTotalIronCost}</td></tr>");
            html.AppendLine($"<tr><td>تكاليف التعويج</td><td>{report.FormattedTotalBendingCost}</td></tr>");
            html.AppendLine($"<tr><td>تكاليف اللحام</td><td>{report.FormattedTotalWeldingCost}</td></tr>");
            html.AppendLine($"<tr style='font-weight: bold; background-color: #1565C0; color: white;'><td>الإجمالي</td><td>{report.FormattedGrandTotal}</td></tr>");
            html.AppendLine("</table>");

            // تفاصيل الديون
            if (debts.Any())
            {
                html.AppendLine("<h2>تفاصيل الديون</h2>");
                html.AppendLine("<table>");
                html.AppendLine("<tr><th>التاريخ</th><th>اسم الشخص</th><th>الوصف</th><th>التكلفة الإجمالية</th><th>الحالة</th></tr>");
                
                foreach (var debt in debts.OrderByDescending(d => d.Date))
                {
                    var statusColor = debt.IsSettled ? "green" : (debt.IsOverdue ? "red" : "orange");
                    html.AppendLine($"<tr>");
                    html.AppendLine($"<td>{debt.Date:yyyy/MM/dd}</td>");
                    html.AppendLine($"<td>{debt.PersonName}</td>");
                    html.AppendLine($"<td>{debt.Description ?? "غير محدد"}</td>");
                    html.AppendLine($"<td>{debt.FormattedTotalCost}</td>");
                    html.AppendLine($"<td style='color: {statusColor};'>{debt.StatusText}</td>");
                    html.AppendLine($"</tr>");
                }
                
                html.AppendLine("</table>");
            }

            html.AppendLine($"<p style='text-align: center; color: #666; margin-top: 40px;'>تم إنشاء التقرير في: {DateTime.Now:yyyy/MM/dd HH:mm:ss}</p>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// محاكاة تحويل HTML إلى PDF
        /// </summary>
        private static string ConvertHtmlToPdfSimulation(string htmlContent)
        {
            // هذه محاكاة - في التطبيق الحقيقي سنستخدم مكتبة مثل iTextSharp أو PuppeteerSharp
            var pdfSimulation = new StringBuilder();
            pdfSimulation.AppendLine("=== تقرير PDF (محاكاة) ===");
            pdfSimulation.AppendLine("ملاحظة: هذا ملف نصي يحاكي PDF. سيتم تطوير تصدير PDF الحقيقي لاحقاً.");
            pdfSimulation.AppendLine();
            pdfSimulation.AppendLine("المحتوى:");
            pdfSimulation.AppendLine(htmlContent);
            
            return pdfSimulation.ToString();
        }

        /// <summary>
        /// إنشاء تقرير CSV
        /// </summary>
        private static string GenerateCsvReport(ReportData report, List<Debt> debts)
        {
            var csv = new StringBuilder();
            
            // إضافة BOM للدعم العربي في Excel
            csv.Append('\ufeff');
            
            // معلومات التقرير
            csv.AppendLine($"تقرير,{report.ReportType}");
            csv.AppendLine($"الفترة,{report.FormattedDateRange}");
            csv.AppendLine($"تاريخ الإنشاء,{DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            csv.AppendLine();

            // الإحصائيات
            csv.AppendLine("الإحصائيات");
            csv.AppendLine("البيان,القيمة");
            csv.AppendLine($"إجمالي المعاملات,{report.TotalTransactions}");
            csv.AppendLine($"المعاملات المسددة,{report.SettledTransactions}");
            csv.AppendLine($"المعاملات غير المسددة,{report.UnsettledTransactions}");
            csv.AppendLine($"المعاملات المتأخرة,{report.OverdueTransactions}");
            csv.AppendLine($"معدل التسديد,{report.FormattedSettlementRate}");
            csv.AppendLine();

            // التكاليف
            csv.AppendLine("التكاليف");
            csv.AppendLine("نوع التكلفة,المبلغ");
            csv.AppendLine($"تكاليف القطع,{report.TotalCuttingCost}");
            csv.AppendLine($"تكاليف الحديد,{report.TotalIronCost}");
            csv.AppendLine($"تكاليف التعويج,{report.TotalBendingCost}");
            csv.AppendLine($"تكاليف اللحام,{report.TotalWeldingCost}");
            csv.AppendLine($"الإجمالي العام,{report.GrandTotal}");
            csv.AppendLine();

            // تفاصيل الديون
            if (debts.Any())
            {
                csv.AppendLine("تفاصيل الديون");
                csv.AppendLine("التاريخ,اسم الشخص,الوصف,تكلفة القطع,تكلفة الحديد,تكلفة التعويج,تكلفة اللحام,التكلفة الإجمالية,الحالة,نوع الوصل");
                
                foreach (var debt in debts.OrderByDescending(d => d.Date))
                {
                    csv.AppendLine($"{debt.Date:yyyy/MM/dd}," +
                                  $"{debt.PersonName}," +
                                  $"\"{debt.Description ?? "غير محدد"}\"," +
                                  $"{debt.CuttingCost}," +
                                  $"{debt.IronCost}," +
                                  $"{debt.BendingCost}," +
                                  $"{debt.WeldingCost}," +
                                  $"{debt.TotalCost}," +
                                  $"{debt.SettlementStatus}," +
                                  $"{debt.ConnectionStatusText}");
                }
            }

            return csv.ToString();
        }

        /// <summary>
        /// إنشاء تقرير نصي
        /// </summary>
        private static string GenerateTextReport(ReportData report, List<Debt> debts)
        {
            var text = new StringBuilder();
            
            text.AppendLine("=".PadLeft(80, '='));
            text.AppendLine($"تقرير {report.ReportType}".PadLeft(40 + report.ReportType.Length / 2));
            text.AppendLine($"الفترة: {report.FormattedDateRange}".PadLeft(40 + report.FormattedDateRange.Length / 2));
            text.AppendLine($"تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm:ss}".PadLeft(50));
            text.AppendLine("=".PadLeft(80, '='));
            text.AppendLine();

            // الإحصائيات
            text.AppendLine("📊 ملخص الإحصائيات:");
            text.AppendLine("-".PadLeft(40, '-'));
            text.AppendLine($"إجمالي المعاملات: {report.TotalTransactions}");
            text.AppendLine($"المعاملات المسددة: {report.SettledTransactions} ({report.FormattedSettlementRate})");
            text.AppendLine($"المعاملات غير المسددة: {report.UnsettledTransactions}");
            text.AppendLine($"المعاملات المتأخرة: {report.OverdueTransactions}");
            text.AppendLine();

            // التكاليف
            text.AppendLine("💰 تفاصيل التكاليف:");
            text.AppendLine("-".PadLeft(40, '-'));
            text.AppendLine($"تكاليف القطع: {report.FormattedTotalCuttingCost}");
            text.AppendLine($"تكاليف الحديد: {report.FormattedTotalIronCost}");
            text.AppendLine($"تكاليف التعويج: {report.FormattedTotalBendingCost}");
            text.AppendLine($"تكاليف اللحام: {report.FormattedTotalWeldingCost}");
            text.AppendLine("-".PadLeft(40, '-'));
            text.AppendLine($"الإجمالي العام: {report.FormattedGrandTotal}");
            text.AppendLine();

            // تفاصيل الديون
            if (debts.Any())
            {
                text.AppendLine("📋 تفاصيل الديون:");
                text.AppendLine("-".PadLeft(80, '-'));
                
                foreach (var debt in debts.OrderByDescending(d => d.Date))
                {
                    text.AppendLine($"التاريخ: {debt.Date:yyyy/MM/dd} | الشخص: {debt.PersonName}");
                    text.AppendLine($"الوصف: {debt.Description ?? "غير محدد"}");
                    text.AppendLine($"التكلفة: {debt.FormattedTotalCost} | الحالة: {debt.StatusText}");
                    text.AppendLine($"نوع الوصل: {debt.ConnectionStatusText}");
                    text.AppendLine();
                }
            }

            text.AppendLine("=".PadLeft(80, '='));
            text.AppendLine("تم إنشاء هذا التقرير بواسطة نظام إدارة الديون المتقدم");
            text.AppendLine("=".PadLeft(80, '='));

            return text.ToString();
        }

        #region Advanced Report Export Methods

        /// <summary>
        /// تصدير تقرير العملاء إلى Excel
        /// </summary>
        public static bool ExportCustomerReportToExcel(List<CustomerReport> customerReports)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel files (*.csv)|*.csv|All files (*.*)|*.*",
                    DefaultExt = "csv",
                    FileName = $"تقرير_العملاء_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() != true)
                    return false;

                var csv = new StringBuilder();
                csv.Append('\ufeff'); // BOM للعربية

                // العنوان
                csv.AppendLine($"تقرير العملاء المتقدم - {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                csv.AppendLine();

                // الرؤوس
                csv.AppendLine("اسم العميل,الهاتف,الموقع,عدد المعاملات,المبلغ الإجمالي,المبلغ المدفوع,المبلغ المتبقي,المعاملات المتأخرة,معدل الدفع,التقييم,آخر معاملة");

                // البيانات
                foreach (var customer in customerReports)
                {
                    csv.AppendLine($"{customer.CustomerName}," +
                                  $"{customer.Phone}," +
                                  $"{customer.Location}," +
                                  $"{customer.TotalTransactions}," +
                                  $"{customer.TotalAmount}," +
                                  $"{customer.PaidAmount}," +
                                  $"{customer.RemainingAmount}," +
                                  $"{customer.OverdueTransactions}," +
                                  $"{customer.PaymentRate:F1}%," +
                                  $"{customer.CustomerRating}," +
                                  $"{customer.LastTransactionDate:yyyy/MM/dd}");
                }

                File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                MessageBox.Show($"تم تصدير تقرير العملاء بنجاح إلى:\n{saveDialog.FileName}",
                    "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير تقرير العملاء:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير تقرير الأرباح والخسائر إلى Excel
        /// </summary>
        public static bool ExportProfitLossReportToExcel(ProfitLossReport profitLossReport)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel files (*.csv)|*.csv|All files (*.*)|*.*",
                    DefaultExt = "csv",
                    FileName = $"تقرير_الأرباح_والخسائر_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() != true)
                    return false;

                var csv = new StringBuilder();
                csv.Append('\ufeff'); // BOM للعربية

                // العنوان
                csv.AppendLine($"تقرير الأرباح والخسائر - {profitLossReport.DateRange}");
                csv.AppendLine($"تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                csv.AppendLine();

                // الملخص المالي
                csv.AppendLine("الملخص المالي");
                csv.AppendLine("البيان,المبلغ");
                csv.AppendLine($"إجمالي الإيرادات,{profitLossReport.TotalRevenue}");
                csv.AppendLine($"إجمالي التكاليف,{profitLossReport.TotalCosts}");
                csv.AppendLine($"الربح الإجمالي,{profitLossReport.GrossProfit}");
                csv.AppendLine($"المصروفات التشغيلية,{profitLossReport.OperatingExpenses}");
                csv.AppendLine($"صافي الربح,{profitLossReport.NetProfit}");
                csv.AppendLine($"هامش الربح,{profitLossReport.ProfitMargin:F1}%");
                csv.AppendLine();

                // البيانات الشهرية
                if (profitLossReport.MonthlyData.Any())
                {
                    csv.AppendLine("البيانات الشهرية");
                    csv.AppendLine("الشهر,الإيرادات,التكاليف,الربح,عدد المعاملات");

                    foreach (var monthly in profitLossReport.MonthlyData)
                    {
                        csv.AppendLine($"{monthly.Month}," +
                                      $"{monthly.Revenue}," +
                                      $"{monthly.Costs}," +
                                      $"{monthly.Profit}," +
                                      $"{monthly.Transactions}");
                    }
                    csv.AppendLine();
                }

                // تحليل الفئات
                if (profitLossReport.CategoryBreakdown.Any())
                {
                    csv.AppendLine("تحليل الفئات");
                    csv.AppendLine("الفئة,الإيرادات,التكاليف,الربح,هامش الربح");

                    foreach (var category in profitLossReport.CategoryBreakdown)
                    {
                        csv.AppendLine($"{category.Category}," +
                                      $"{category.Revenue}," +
                                      $"{category.Costs}," +
                                      $"{category.Profit}," +
                                      $"{category.ProfitMargin:F1}%");
                    }
                }

                File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                MessageBox.Show($"تم تصدير تقرير الأرباح والخسائر بنجاح إلى:\n{saveDialog.FileName}",
                    "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير تقرير الأرباح والخسائر:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير تقرير التوقعات إلى Excel
        /// </summary>
        public static bool ExportForecastReportToExcel(ForecastReport forecastReport)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel files (*.csv)|*.csv|All files (*.*)|*.*",
                    DefaultExt = "csv",
                    FileName = $"تقرير_التوقعات_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() != true)
                    return false;

                var csv = new StringBuilder();
                csv.Append('\ufeff'); // BOM للعربية

                // العنوان
                csv.AppendLine($"تقرير التوقعات والتنبؤات - {forecastReport.ForecastPeriod}");
                csv.AppendLine($"تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
                csv.AppendLine($"طريقة التوقع: {forecastReport.ForecastMethod}");
                csv.AppendLine();

                // ملخص التوقعات
                csv.AppendLine("ملخص التوقعات");
                csv.AppendLine("البيان,القيمة");
                csv.AppendLine($"الإيرادات المتوقعة,{forecastReport.PredictedRevenue}");
                csv.AppendLine($"التكاليف المتوقعة,{forecastReport.PredictedCosts}");
                csv.AppendLine($"الربح المتوقع,{forecastReport.PredictedProfit}");
                csv.AppendLine($"المعاملات المتوقعة,{forecastReport.PredictedTransactions}");
                csv.AppendLine($"دقة التوقع,{forecastReport.AccuracyScore:F1}%");
                csv.AppendLine();

                // التوقعات اليومية
                if (forecastReport.DailyForecasts.Any())
                {
                    csv.AppendLine("التوقعات اليومية");
                    csv.AppendLine("التاريخ,الإيرادات المتوقعة,المعاملات المتوقعة,مستوى الثقة");

                    foreach (var daily in forecastReport.DailyForecasts.Take(30)) // أول 30 يوم
                    {
                        csv.AppendLine($"{daily.Date:yyyy/MM/dd}," +
                                      $"{daily.PredictedRevenue}," +
                                      $"{daily.PredictedTransactions}," +
                                      $"{daily.Confidence:F1}%");
                    }
                }

                File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                MessageBox.Show($"تم تصدير تقرير التوقعات بنجاح إلى:\n{saveDialog.FileName}",
                    "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير تقرير التوقعات:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير تقرير ديون شخص معين إلى PDF
        /// </summary>
        public static bool ExportPersonDebtsToPdf(string personName, List<Debt> debts, Models.Person? person = null, string? filePath = null)
        {
            try
            {
                if (filePath == null)
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "PDF files (*.pdf)|*.pdf|All files (*.*)|*.*",
                        DefaultExt = "pdf",
                        FileName = $"تقرير_ديون_{personName}_{DateTime.Now:yyyyMMdd}.pdf"
                    };

                    if (saveDialog.ShowDialog() != true)
                        return false;

                    filePath = saveDialog.FileName;
                }

                // التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في إنشاء المجلد:\n{ex.Message}",
                            "خطأ في الوصول للملف", MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

                // التحقق من أن الملف غير مستخدم وإنشاء اسم بديل إذا لزم الأمر
                if (IsFileInUse(filePath))
                {
                    // إنشاء اسم ملف جديد
                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
                    var extension = Path.GetExtension(filePath);
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var newFileName = $"{fileNameWithoutExtension}_{timestamp}{extension}";
                    filePath = Path.Combine(directory ?? "", newFileName);

                    MessageBox.Show($"الملف الأصلي مفتوح في برنامج آخر.\nسيتم حفظ التقرير باسم جديد:\n{newFileName}",
                        "تم تغيير اسم الملف", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                // إنشاء PDF باستخدام PdfSharp مع تحسينات للعربية
                try
                {
                    CreatePdfWithTables(filePath, personName, debts, person);
                }
                catch (UnauthorizedAccessException ex)
                {
                    MessageBox.Show($"ليس لديك صلاحية للكتابة في هذا المكان:\n{filePath}\n\nالخطأ: {ex.Message}",
                        "خطأ في الصلاحيات", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
                catch (DirectoryNotFoundException ex)
                {
                    MessageBox.Show($"المجلد غير موجود:\n{Path.GetDirectoryName(filePath)}\n\nالخطأ: {ex.Message}",
                        "خطأ في المسار", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
                catch (IOException ex)
                {
                    MessageBox.Show($"خطأ في الوصول للملف:\n{filePath}\n\nالخطأ: {ex.Message}\n\nتأكد من أن الملف غير مفتوح في برنامج آخر.",
                        "خطأ في الوصول للملف", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في إنشاء PDF:\n\nالخطأ: {ex.Message}",
                        "خطأ في إنشاء PDF", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                MessageBox.Show($"تم تصدير تقرير ديون {personName} بنجاح إلى:\n{filePath}", "نجح التصدير",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير تقرير ديون {personName}:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء تقرير HTML بنفس تصميم الطباعة مع اللوجو
        /// </summary>
        private static string GeneratePersonDebtsReportWithLogo(string personName, List<Debt> debts, Models.Person? person)
        {
            var html = new StringBuilder();

            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine($"<title>تقرير ديون {personName}</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: 'Arial', sans-serif; margin: 25px; direction: rtl; font-size: 11pt; }");
            html.AppendLine("@page { size: A4; margin: 25px; }");

            // تنسيق اللوجو
            html.AppendLine(".logo-container { text-align: center; margin-bottom: 20px; }");
            html.AppendLine(".logo { max-height: 80px; max-width: 120px; }");

            // تنسيق العناوين
            html.AppendLine(".main-title { font-size: 18pt; font-weight: bold; text-align: center; margin: 15px 0; }");
            html.AppendLine(".header-info { font-size: 11pt; text-align: center; margin: 10px 0; }");
            html.AppendLine(".statistics { font-size: 11pt; text-align: center; margin: 15px 0; font-weight: bold; }");

            // تنسيق الجدول بنفس تصميم الطباعة
            html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 8pt; }");
            html.AppendLine("th, td { border: 1px solid black; padding: 2px; text-align: center; }");
            html.AppendLine("th { background-color: #d3d3d3; font-weight: bold; font-size: 9pt; line-height: 11pt; }");
            html.AppendLine("td { font-size: 8pt; line-height: 10pt; }");

            // تنسيق التذييل
            html.AppendLine(".footer { font-size: 8pt; text-align: center; margin-top: 15px; color: gray; }");

            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // إضافة اللوجو إذا كان متوفراً
            AddLogoToHtml(html);

            // عنوان التقرير
            html.AppendLine($"<div class='main-title'>تقرير ديون {personName}</div>");

            // معلومات الشخص والتاريخ
            var phoneText = person?.Phone ?? "غير محدد";
            html.AppendLine($"<div class='header-info'>الاسم: {personName} | الهاتف: {phoneText} | التاريخ: {DateTime.Now:yyyy/MM/dd}</div>");

            // إحصائيات الديون
            var totalAmount = debts.Sum(d => d.Amount);
            var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
            var pendingAmount = totalAmount - settledAmount;
            var settledCount = debts.Count(d => d.IsSettled);
            var pendingCount = debts.Count(d => !d.IsSettled);

            html.AppendLine($"<div class='statistics'>إجمالي: {debts.Count} دين ({totalAmount:N0} د.ع) | مسدد: {settledCount} ({settledAmount:N0} د.ع) | متبقي: {pendingCount} ({pendingAmount:N0} د.ع)</div>");

            // جدول الديون بنفس تصميم الطباعة
            html.AppendLine("<table>");
            html.AppendLine("<thead>");
            html.AppendLine("<tr>");
            html.AppendLine("<th style='width: 13%;'>المبلغ (د.ع)</th>");
            html.AppendLine("<th style='width: 8%;'>العدد</th>");
            html.AppendLine("<th style='width: 10%;'>سعر المفرد</th>");
            html.AppendLine("<th style='width: 10%;'>تاريخ الدين</th>");
            html.AppendLine("<th style='width: 10%;'>تاريخ الاستحقاق</th>");
            html.AppendLine("<th style='width: 12%;'>نوع العملية</th>");
            html.AppendLine("<th style='width: 20%;'>الوصف</th>");
            html.AppendLine("<th style='width: 12%;'>الملاحظات</th>");
            html.AppendLine("<th style='width: 5%;'>الحالة</th>");
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");
            html.AppendLine("<tbody>");

            foreach (var debt in debts.OrderByDescending(d => d.Date))
            {
                html.AppendLine("<tr>");
                html.AppendLine($"<td>{debt.Amount:N0}</td>");
                html.AppendLine($"<td>{debt.Quantity?.ToString("N0") ?? "1"}</td>");
                html.AppendLine($"<td>{debt.UnitPrice?.ToString("N0") ?? "0"}</td>");
                html.AppendLine($"<td>{debt.Date:yyyy/MM/dd}</td>");
                html.AppendLine($"<td>{debt.DueDate:yyyy/MM/dd}</td>");
                html.AppendLine($"<td>{debt.OperationType ?? "غير محدد"}</td>");
                html.AppendLine($"<td>{debt.Description ?? ""}</td>");
                html.AppendLine($"<td>{debt.Notes ?? ""}</td>");
                html.AppendLine($"<td>{(debt.IsSettled ? "مسدد" : "غير مسدد")}</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");

            // تذييل التقرير
            html.AppendLine($"<div class='footer'>نظام إدارة الديون - {DateTime.Now:yyyy/MM/dd HH:mm}</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// إنشاء تقرير نصي منسق بنفس تصميم التقارير الشاملة
        /// </summary>
        private static string GeneratePersonDebtsTextReport(string personName, List<Debt> debts, Models.Person? person)
        {
            var report = new StringBuilder();

            // رأس التقرير مع معلومات الشركة
            AddCompanyHeaderToReport(report);

            // عنوان التقرير
            report.AppendLine();
            report.AppendLine("".PadLeft(80, '='));
            report.AppendLine($"تقرير ديون {personName}".PadLeft(50));
            report.AppendLine("".PadLeft(80, '='));
            report.AppendLine();

            // معلومات الشخص والتاريخ
            var phoneText = person?.Phone ?? "غير محدد";
            report.AppendLine($"الاسم: {personName}");
            report.AppendLine($"الهاتف: {phoneText}");
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            report.AppendLine();

            // إحصائيات الديون
            var totalAmount = debts.Sum(d => d.Amount);
            var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
            var pendingAmount = totalAmount - settledAmount;
            var settledCount = debts.Count(d => d.IsSettled);
            var pendingCount = debts.Count(d => !d.IsSettled);

            report.AppendLine("ملخص الديون:");
            report.AppendLine("".PadLeft(40, '-'));
            report.AppendLine($"إجمالي الديون: {debts.Count} دين");
            report.AppendLine($"إجمالي المبلغ: {totalAmount:N0} دينار عراقي");
            report.AppendLine($"الديون المسددة: {settledCount} دين ({settledAmount:N0} د.ع)");
            report.AppendLine($"الديون المتبقية: {pendingCount} دين ({pendingAmount:N0} د.ع)");
            report.AppendLine();

            // جدول الديون
            report.AppendLine("تفاصيل الديون:");
            report.AppendLine("".PadLeft(120, '='));

            // رأس الجدول
            var header = string.Format("{0,-12} {1,-8} {2,-10} {3,-12} {4,-12} {5,-15} {6,-20} {7,-15} {8,-10}",
                "المبلغ", "العدد", "سعر المفرد", "تاريخ الدين", "تاريخ الاستحقاق", "نوع العملية", "الوصف", "الملاحظات", "الحالة");
            report.AppendLine(header);
            report.AppendLine("".PadLeft(120, '-'));

            // بيانات الجدول
            foreach (var debt in debts.OrderByDescending(d => d.Date))
            {
                var row = string.Format("{0,-12} {1,-8} {2,-10} {3,-12} {4,-12} {5,-15} {6,-20} {7,-15} {8,-10}",
                    debt.Amount.ToString("N0"),
                    debt.Quantity?.ToString("N0") ?? "1",
                    debt.UnitPrice?.ToString("N0") ?? "0",
                    debt.Date.ToString("yyyy/MM/dd"),
                    debt.DueDate.ToString("yyyy/MM/dd"),
                    TruncateText(debt.OperationType ?? "غير محدد", 14),
                    TruncateText(debt.Description ?? "", 19),
                    TruncateText(debt.Notes ?? "", 14),
                    debt.IsSettled ? "مسدد" : "غير مسدد");
                report.AppendLine(row);
            }

            report.AppendLine("".PadLeft(120, '='));

            // تذييل التقرير
            report.AppendLine();
            report.AppendLine("".PadLeft(80, '-'));
            report.AppendLine($"نظام إدارة الديون - {DateTime.Now:yyyy/MM/dd HH:mm}".PadLeft(60));
            report.AppendLine("".PadLeft(80, '-'));

            return report.ToString();
        }

        /// <summary>
        /// إضافة رأس الشركة للتقرير
        /// </summary>
        private static void AddCompanyHeaderToReport(StringBuilder report)
        {
            try
            {
                // معلومات الشركة من الإعدادات
                var companyName = DebtManagementApp.Properties.Settings.Default.CompanyName;
                var companyAddress = DebtManagementApp.Properties.Settings.Default.CompanyAddress;
                var companyPhone = DebtManagementApp.Properties.Settings.Default.CompanyPhone;
                var companyEmail = DebtManagementApp.Properties.Settings.Default.CompanyEmail;

                if (!string.IsNullOrEmpty(companyName))
                {
                    report.AppendLine("".PadLeft(80, '='));
                    report.AppendLine(companyName.PadLeft(50));

                    if (!string.IsNullOrEmpty(companyAddress))
                        report.AppendLine(companyAddress.PadLeft(45));

                    if (!string.IsNullOrEmpty(companyPhone))
                        report.AppendLine($"هاتف: {companyPhone}".PadLeft(40));

                    if (!string.IsNullOrEmpty(companyEmail))
                        report.AppendLine($"البريد الإلكتروني: {companyEmail}".PadLeft(45));

                    report.AppendLine("".PadLeft(80, '='));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة رأس الشركة: {ex.Message}");
            }
        }

        /// <summary>
        /// قطع النص إذا كان أطول من الحد المسموح
        /// </summary>
        private static string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            return text.Length <= maxLength ? text : text.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// إضافة اللوجو إلى HTML إذا كان متوفراً
        /// </summary>
        private static void AddLogoToHtml(StringBuilder html)
        {
            try
            {
                string logoPath = DebtManagementApp.Properties.Settings.Default.CompanyLogoPath;

                if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                {
                    // تحويل الصورة إلى Base64 لتضمينها في HTML
                    byte[] imageBytes = File.ReadAllBytes(logoPath);
                    string base64String = Convert.ToBase64String(imageBytes);
                    string extension = Path.GetExtension(logoPath).ToLower();
                    string mimeType = extension switch
                    {
                        ".jpg" or ".jpeg" => "image/jpeg",
                        ".png" => "image/png",
                        ".gif" => "image/gif",
                        ".bmp" => "image/bmp",
                        _ => "image/png"
                    };

                    html.AppendLine("<div class='logo-container'>");
                    html.AppendLine($"<img src='data:{mimeType};base64,{base64String}' class='logo' alt='شعار الشركة' />");
                    html.AppendLine("</div>");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة اللوجو إلى HTML: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء تقرير HTML مخصص لديون شخص معين (النسخة القديمة)
        /// </summary>
        private static string GeneratePersonDebtsHtmlReport(string personName, List<Debt> debts)
        {
            var html = new StringBuilder();

            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine($"<title>تقرير ديون {personName}</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }");
            html.AppendLine("h1 { color: #2c3e50; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 10px; }");
            html.AppendLine("h2 { color: #34495e; margin-top: 30px; }");
            html.AppendLine("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }");
            html.AppendLine("th { background-color: #3498db; color: white; font-weight: bold; }");
            html.AppendLine("tr:nth-child(even) { background-color: #f2f2f2; }");
            html.AppendLine(".summary { background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }");
            html.AppendLine(".settled { color: #27ae60; font-weight: bold; }");
            html.AppendLine(".unsettled { color: #e74c3c; font-weight: bold; }");
            html.AppendLine(".overdue { color: #e67e22; font-weight: bold; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // عنوان التقرير
            html.AppendLine($"<h1>تقرير ديون {personName}</h1>");
            html.AppendLine($"<p style='text-align: center; color: #7f8c8d;'>تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm:ss}</p>");

            // ملخص الإحصائيات
            var totalAmount = debts.Sum(d => d.Amount);
            var paidAmount = debts.Sum(d => d.PaidAmount);
            var remainingAmount = debts.Sum(d => d.RemainingAmount);
            var settledCount = debts.Count(d => d.IsSettled);
            var overdueCount = debts.Count(d => !d.IsSettled && d.DueDate < DateTime.Now);

            html.AppendLine("<div class='summary'>");
            html.AppendLine("<h2>📊 ملخص الإحصائيات</h2>");
            html.AppendLine($"<p><strong>إجمالي عدد الديون:</strong> {debts.Count}</p>");
            html.AppendLine($"<p><strong>إجمالي المبلغ:</strong> {totalAmount:N0} دينار عراقي</p>");
            html.AppendLine($"<p><strong>المبلغ المدفوع:</strong> {paidAmount:N0} دينار عراقي</p>");
            html.AppendLine($"<p><strong>المبلغ المتبقي:</strong> {remainingAmount:N0} دينار عراقي</p>");
            html.AppendLine($"<p><strong>الديون المسددة:</strong> <span class='settled'>{settledCount}</span></p>");
            html.AppendLine($"<p><strong>الديون غير المسددة:</strong> <span class='unsettled'>{debts.Count - settledCount}</span></p>");
            html.AppendLine($"<p><strong>الديون المتأخرة:</strong> <span class='overdue'>{overdueCount}</span></p>");
            html.AppendLine("</div>");

            // جدول تفاصيل الديون
            html.AppendLine("<h2>📋 تفاصيل الديون</h2>");
            html.AppendLine("<table>");
            html.AppendLine("<thead>");
            html.AppendLine("<tr>");
            html.AppendLine("<th>المبلغ</th>");
            html.AppendLine("<th>المبلغ المدفوع</th>");
            html.AppendLine("<th>المبلغ المتبقي</th>");
            html.AppendLine("<th>تاريخ الإنشاء</th>");
            html.AppendLine("<th>تاريخ الاستحقاق</th>");
            html.AppendLine("<th>نوع العملية</th>");
            html.AppendLine("<th>الوصف</th>");
            html.AppendLine("<th>الحالة</th>");
            html.AppendLine("</tr>");
            html.AppendLine("</thead>");
            html.AppendLine("<tbody>");

            foreach (var debt in debts.OrderByDescending(d => d.Date))
            {
                var statusClass = debt.IsSettled ? "settled" :
                                 (debt.DueDate < DateTime.Now ? "overdue" : "unsettled");
                var statusText = debt.IsSettled ? "مسدد" :
                                (debt.DueDate < DateTime.Now ? "متأخر" : "غير مسدد");

                html.AppendLine("<tr>");
                html.AppendLine($"<td>{debt.Amount:N0}</td>");
                html.AppendLine($"<td>{debt.PaidAmount:N0}</td>");
                html.AppendLine($"<td>{debt.RemainingAmount:N0}</td>");
                html.AppendLine($"<td>{debt.Date:yyyy/MM/dd}</td>");
                html.AppendLine($"<td>{debt.DueDate:yyyy/MM/dd}</td>");
                html.AppendLine($"<td>{debt.OperationType ?? "غير محدد"}</td>");
                html.AppendLine($"<td>{debt.Description ?? ""}</td>");
                html.AppendLine($"<td class='{statusClass}'>{statusText}</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");

            // تذييل التقرير
            html.AppendLine("<div style='margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 12px;'>");
            html.AppendLine("<p>تم إنشاء هذا التقرير بواسطة نظام إدارة الديون</p>");
            html.AppendLine($"<p>© {DateTime.Now.Year} - جميع الحقوق محفوظة</p>");
            html.AppendLine("</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// إنشاء PDF حقيقي مع جداول باستخدام PdfSharp
        /// </summary>
        private static void CreatePdfWithTables(string filePath, string personName, List<Debt> debts, Models.Person? person)
        {
            // إنشاء مستند PDF جديد
            var document = new PdfSharp.Pdf.PdfDocument();
            var page = document.AddPage();
            var gfx = XGraphics.FromPdfPage(page);

            // إعداد الخطوط - استخدام خط يدعم العربية بشكل أفضل
            var titleFont = new XFont("Tahoma", 16, XFontStyleEx.Bold);
            var headerFont = new XFont("Tahoma", 12, XFontStyleEx.Bold);
            var normalFont = new XFont("Tahoma", 10, XFontStyleEx.Regular);
            var smallFont = new XFont("Tahoma", 8, XFontStyleEx.Regular);

            double yPosition = 50;
            double leftMargin = 50;
            double rightMargin = page.Width.Point - 50;

            // إضافة اللوغو أولاً
            yPosition = AddLogoToPdfSharp(gfx, yPosition);

            // إضافة معلومات الشركة
            yPosition = AddCompanyHeaderToPdfSharp(gfx, yPosition, leftMargin, rightMargin, headerFont, normalFont);

            // عنوان التقرير
            var title = FixArabicText($"تقرير ديون {personName}");
            var titleSize = gfx.MeasureString(title, titleFont);
            gfx.DrawString(title, titleFont, XBrushes.Black,
                new XPoint((page.Width.Point - titleSize.Width) / 2, yPosition));
            yPosition += 30;

            // معلومات الشخص
            var phoneText = person?.Phone ?? "غير محدد";
            var personInfo = FixArabicText($"الاسم: {personName} | الهاتف: {phoneText} | التاريخ: {DateTime.Now:yyyy/MM/dd}");
            var personInfoSize = gfx.MeasureString(personInfo, normalFont);
            gfx.DrawString(personInfo, normalFont, XBrushes.Black,
                new XPoint((page.Width.Point - personInfoSize.Width) / 2, yPosition));
            yPosition += 25;

            // إحصائيات الديون
            yPosition = AddDebtStatisticsToPdfSharp(gfx, yPosition, leftMargin, debts, headerFont, normalFont);

            // جدول الديون
            AddDebtsTableToPdfSharp(gfx, yPosition, leftMargin, rightMargin, debts, headerFont, smallFont);

            // حفظ المستند
            document.Save(filePath);
            document.Close();
        }

        /// <summary>
        /// إضافة اللوغو إلى PDF باستخدام PdfSharp
        /// </summary>
        private static double AddLogoToPdfSharp(XGraphics gfx, double yPosition)
        {
            try
            {
                string logoPath = DebtManagementApp.Properties.Settings.Default.CompanyLogoPath;

                if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                {
                    // تحميل الصورة
                    using (var bitmap = new Bitmap(logoPath))
                    {
                        // تحويل إلى XImage
                        using (var stream = new MemoryStream())
                        {
                            bitmap.Save(stream, ImageFormat.Png);
                            stream.Position = 0;
                            var xImage = XImage.FromStream(stream);

                            // حساب الأبعاد (الحد الأقصى 120x80)
                            double maxWidth = 120;
                            double maxHeight = 80;
                            double aspectRatio = (double)xImage.PixelWidth / xImage.PixelHeight;

                            double width = maxWidth;
                            double height = maxWidth / aspectRatio;

                            if (height > maxHeight)
                            {
                                height = maxHeight;
                                width = maxHeight * aspectRatio;
                            }

                            // رسم الصورة في المنتصف
                            double xPos = (gfx.PageSize.Width - width) / 2;
                            gfx.DrawImage(xImage, xPos, yPosition, width, height);

                            yPosition += height + 20; // إضافة مسافة بعد اللوغو
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة اللوغو: {ex.Message}");
            }

            return yPosition;
        }

        /// <summary>
        /// التحقق من أن الملف غير مستخدم من قبل عملية أخرى
        /// </summary>
        private static bool IsFileInUse(string filePath)
        {
            // إذا كان الملف غير موجود، فهو غير مستخدم
            if (!File.Exists(filePath))
                return false;

            try
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None))
                {
                    // إذا تمكنا من فتح الملف، فهو غير مستخدم
                    return false;
                }
            }
            catch (UnauthorizedAccessException)
            {
                // مشكلة في الصلاحيات
                return true;
            }
            catch (IOException)
            {
                // إذا فشل فتح الملف، فهو مستخدم من قبل عملية أخرى
                return true;
            }
            catch
            {
                // في حالة أي خطأ آخر، نعتبر الملف مستخدم
                return true;
            }
        }

        /// <summary>
        /// إصلاح النص العربي للعرض الصحيح في PDF
        /// </summary>
        private static string FixArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            try
            {
                // تحويل النص إلى مصفوفة من الكلمات
                var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);

                // عكس ترتيب الكلمات للحصول على اتجاه RTL
                Array.Reverse(words);

                // إرجاع النص مع الكلمات معكوسة
                return string.Join(" ", words);
            }
            catch
            {
                // في حالة حدوث خطأ، إرجاع النص الأصلي
                return text;
            }
        }

        /// <summary>
        /// فحص ما إذا كان النص يحتوي على أحرف عربية
        /// </summary>
        private static bool IsArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // فحص وجود أحرف عربية
            foreach (char c in text)
            {
                if ((c >= 0x0600 && c <= 0x06FF) || // النطاق العربي الأساسي
                    (c >= 0xFE70 && c <= 0xFEFF) || // النطاق العربي المُشكَّل
                    (c >= 0xFB50 && c <= 0xFDFF))   // النطاق العربي الإضافي
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// إضافة رأس الشركة إلى PDF باستخدام PdfSharp
        /// </summary>
        private static double AddCompanyHeaderToPdfSharp(XGraphics gfx, double yPosition, double leftMargin, double rightMargin, XFont headerFont, XFont normalFont)
        {
            try
            {
                var companyName = DebtManagementApp.Properties.Settings.Default.CompanyName;
                var companyAddress = DebtManagementApp.Properties.Settings.Default.CompanyAddress;
                var companyPhone = DebtManagementApp.Properties.Settings.Default.CompanyPhone;
                var companyEmail = DebtManagementApp.Properties.Settings.Default.CompanyEmail;

                if (!string.IsNullOrEmpty(companyName))
                {
                    var fixedCompanyName = FixArabicText(companyName);
                    var companyNameSize = gfx.MeasureString(fixedCompanyName, headerFont);
                    gfx.DrawString(fixedCompanyName, headerFont, XBrushes.Black,
                        new XPoint((gfx.PageSize.Width - companyNameSize.Width) / 2, yPosition));
                    yPosition += 20;

                    if (!string.IsNullOrEmpty(companyAddress))
                    {
                        var fixedAddress = FixArabicText(companyAddress);
                        var addressSize = gfx.MeasureString(fixedAddress, normalFont);
                        gfx.DrawString(fixedAddress, normalFont, XBrushes.Black,
                            new XPoint((gfx.PageSize.Width - addressSize.Width) / 2, yPosition));
                        yPosition += 15;
                    }

                    if (!string.IsNullOrEmpty(companyPhone))
                    {
                        var phoneText = FixArabicText($"هاتف: {companyPhone}");
                        var phoneSize = gfx.MeasureString(phoneText, normalFont);
                        gfx.DrawString(phoneText, normalFont, XBrushes.Black,
                            new XPoint((gfx.PageSize.Width - phoneSize.Width) / 2, yPosition));
                        yPosition += 15;
                    }

                    if (!string.IsNullOrEmpty(companyEmail))
                    {
                        var emailText = FixArabicText($"البريد الإلكتروني: {companyEmail}");
                        var emailSize = gfx.MeasureString(emailText, normalFont);
                        gfx.DrawString(emailText, normalFont, XBrushes.Black,
                            new XPoint((gfx.PageSize.Width - emailSize.Width) / 2, yPosition));
                        yPosition += 15;
                    }

                    // خط فاصل
                    gfx.DrawLine(XPens.Black, leftMargin, yPosition, rightMargin, yPosition);
                    yPosition += 20;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة رأس الشركة إلى PDF: {ex.Message}");
            }

            return yPosition;
        }

        /// <summary>
        /// إضافة إحصائيات الديون إلى PDF باستخدام PdfSharp
        /// </summary>
        private static double AddDebtStatisticsToPdfSharp(XGraphics gfx, double yPosition, double leftMargin, List<Debt> debts, XFont headerFont, XFont normalFont)
        {
            var totalAmount = debts.Sum(d => d.Amount);
            var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
            var pendingAmount = totalAmount - settledAmount;
            var settledCount = debts.Count(d => d.IsSettled);
            var pendingCount = debts.Count(d => !d.IsSettled);

            // عنوان الإحصائيات
            var statsTitle = FixArabicText("ملخص الديون:");
            gfx.DrawString(statsTitle, headerFont, XBrushes.Black, new XPoint(leftMargin, yPosition));
            yPosition += 20;

            // الإحصائيات
            var stats = new string[]
            {
                FixArabicText($"إجمالي الديون: {debts.Count} دين"),
                FixArabicText($"إجمالي المبلغ: {totalAmount:N0} دينار عراقي"),
                FixArabicText($"الديون المسددة: {settledCount} دين ({settledAmount:N0} د.ع)"),
                FixArabicText($"الديون المتبقية: {pendingCount} دين ({pendingAmount:N0} د.ع)")
            };

            foreach (var stat in stats)
            {
                gfx.DrawString(stat, normalFont, XBrushes.Black, new XPoint(leftMargin, yPosition));
                yPosition += 15;
            }

            yPosition += 10; // مسافة إضافية قبل الجدول
            return yPosition;
        }

        /// <summary>
        /// إضافة جدول الديون إلى PDF باستخدام PdfSharp
        /// </summary>
        private static void AddDebtsTableToPdfSharp(XGraphics gfx, double yPosition, double leftMargin, double rightMargin, List<Debt> debts, XFont headerFont, XFont dataFont)
        {
            // عنوان الجدول
            var tableTitle = FixArabicText("تفاصيل الديون:");
            gfx.DrawString(tableTitle, headerFont, XBrushes.Black, new XPoint(leftMargin, yPosition));
            yPosition += 20;

            // إعداد الجدول
            var tableWidth = rightMargin - leftMargin;
            var columnWidths = new double[] { 60, 40, 60, 70, 70, 80, 100, 80, 60 }; // عرض الأعمدة
            var headers = new string[] {
                FixArabicText("المبلغ"),
                FixArabicText("العدد"),
                FixArabicText("سعر المفرد"),
                FixArabicText("تاريخ الدين"),
                FixArabicText("تاريخ الاستحقاق"),
                FixArabicText("نوع العملية"),
                FixArabicText("الوصف"),
                FixArabicText("الملاحظات"),
                FixArabicText("الحالة")
            };

            // رسم رؤوس الأعمدة
            double currentX = leftMargin;
            var headerHeight = 20;

            // خلفية رؤوس الأعمدة
            gfx.DrawRectangle(XBrushes.LightGray, leftMargin, yPosition, tableWidth, headerHeight);
            gfx.DrawRectangle(XPens.Black, leftMargin, yPosition, tableWidth, headerHeight);

            // نص رؤوس الأعمدة
            for (int i = 0; i < headers.Length; i++)
            {
                var headerRect = new XRect(currentX, yPosition, columnWidths[i], headerHeight);
                gfx.DrawString(headers[i], dataFont, XBrushes.Black, headerRect, XStringFormats.Center);

                // خطوط عمودية
                if (i < headers.Length - 1)
                    gfx.DrawLine(XPens.Black, currentX + columnWidths[i], yPosition, currentX + columnWidths[i], yPosition + headerHeight);

                currentX += columnWidths[i];
            }

            yPosition += headerHeight;

            // رسم بيانات الجدول
            var rowHeight = 15;
            foreach (var debt in debts.OrderByDescending(d => d.Date).Take(20)) // أول 20 دين لتجنب تجاوز الصفحة
            {
                currentX = leftMargin;

                // خلفية الصف
                gfx.DrawRectangle(XPens.Black, leftMargin, yPosition, tableWidth, rowHeight);

                var rowData = new string[]
                {
                    debt.Amount.ToString("N0"),
                    debt.Quantity?.ToString("N0") ?? "1",
                    debt.UnitPrice?.ToString("N0") ?? "0",
                    debt.Date.ToString("yyyy/MM/dd"),
                    debt.DueDate.ToString("yyyy/MM/dd"),
                    FixArabicText(TruncateText(debt.OperationType ?? "غير محدد", 10)),
                    FixArabicText(TruncateText(debt.Description ?? "", 15)),
                    FixArabicText(TruncateText(debt.Notes ?? "", 10)),
                    FixArabicText(debt.IsSettled ? "مسدد" : "غير مسدد")
                };

                // نص البيانات
                for (int i = 0; i < rowData.Length; i++)
                {
                    var cellRect = new XRect(currentX, yPosition, columnWidths[i], rowHeight);
                    gfx.DrawString(rowData[i], dataFont, XBrushes.Black, cellRect, XStringFormats.Center);

                    // خطوط عمودية
                    if (i < rowData.Length - 1)
                        gfx.DrawLine(XPens.Black, currentX + columnWidths[i], yPosition, currentX + columnWidths[i], yPosition + rowHeight);

                    currentX += columnWidths[i];
                }

                yPosition += rowHeight;

                // التحقق من عدم تجاوز الصفحة
                if (yPosition > gfx.PageSize.Height - 100)
                    break;
            }

            // تذييل التقرير
            yPosition += 20;
            var footerText = FixArabicText($"نظام إدارة الديون - {DateTime.Now:yyyy/MM/dd HH:mm}");
            var footerSize = gfx.MeasureString(footerText, dataFont);
            gfx.DrawString(footerText, dataFont, XBrushes.Gray,
                new XPoint((gfx.PageSize.Width - footerSize.Width) / 2, yPosition));
        }



        #endregion
    }
}
