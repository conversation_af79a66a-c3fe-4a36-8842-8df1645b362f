﻿#pragma checksum "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4A2FA02902C3F5E99AA872E79B42548EA8A4780C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// GlobalWorkerDetailsBubble
    /// </summary>
    public partial class GlobalWorkerDetailsBubble : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 62 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkerNameLabel;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NameText;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock JobTitleText;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneText;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AddressText;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyWageText;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyWageOutsideText;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeeklyWageText;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FridayInclusionText;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HireDateText;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkDurationText;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExperienceText;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastWorkDateText;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SkillsText;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotesSection;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesText;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditButton;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteButton;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/globalworkerdetailsbubble.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.WorkerNameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.NameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.JobTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PhoneText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.AddressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.DailyWageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.DailyWageOutsideText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.WeeklyWageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.FridayInclusionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.HireDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.WorkDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ExperienceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.LastWorkDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SkillsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.NotesSection = ((System.Windows.Controls.Border)(target));
            return;
            case 19:
            this.NotesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.EditButton = ((System.Windows.Controls.Button)(target));
            
            #line 260 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
            this.EditButton.Click += new System.Windows.RoutedEventHandler(this.EditButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.DeleteButton = ((System.Windows.Controls.Button)(target));
            
            #line 265 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
            this.DeleteButton.Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 270 "..\..\..\..\Views\GlobalWorkerDetailsBubble.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

