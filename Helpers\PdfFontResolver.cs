using System;
using System.IO;
using PdfSharp.Fonts;

namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// Font Resolver لحل مشكلة الخطوط في PdfSharp
    /// </summary>
    public class PdfFontResolver : IFontResolver
    {
        public byte[] GetFont(string faceName)
        {
            try
            {
                // محاولة العثور على الخط في النظام
                string fontPath = GetSystemFontPath(faceName);
                
                if (!string.IsNullOrEmpty(fontPath) && File.Exists(fontPath))
                {
                    return File.ReadAllBytes(fontPath);
                }

                // إذا لم يتم العثور على الخط المطلوب، استخدم خط افتراضي
                return GetDefaultFont();
            }
            catch
            {
                // في حالة الخطأ، إرجاع خط افتراضي
                return GetDefaultFont();
            }
        }

        public FontResolverInfo ResolveTypeface(string familyName, bool isBold, bool isItalic)
        {
            try
            {
                // تحويل أسماء الخطوط العربية إلى خطوط متاحة
                string fontName = GetAvailableFontName(familyName);
                
                // تحديد نمط الخط
                string style = "";
                if (isBold && isItalic)
                    style = "BoldItalic";
                else if (isBold)
                    style = "Bold";
                else if (isItalic)
                    style = "Italic";
                else
                    style = "Regular";

                string faceName = $"{fontName}#{style}";
                
                return new FontResolverInfo(faceName);
            }
            catch
            {
                // في حالة الخطأ، إرجاع خط افتراضي
                return new FontResolverInfo("Arial#Regular");
            }
        }

        private string GetAvailableFontName(string familyName)
        {
            // قائمة الخطوط المتاحة بترتيب الأولوية
            string[] availableFonts = { "Tahoma", "Arial", "Segoe UI", "Calibri", "Times New Roman" };
            
            // إذا كان الخط المطلوب متاحاً، استخدمه
            if (IsFontAvailable(familyName))
                return familyName;
            
            // البحث عن أول خط متاح من القائمة
            foreach (string font in availableFonts)
            {
                if (IsFontAvailable(font))
                    return font;
            }
            
            // إذا لم يتم العثور على أي خط، استخدم Arial كافتراضي
            return "Arial";
        }

        private bool IsFontAvailable(string fontName)
        {
            try
            {
                string fontPath = GetSystemFontPath(fontName);
                return !string.IsNullOrEmpty(fontPath) && File.Exists(fontPath);
            }
            catch
            {
                return false;
            }
        }

        private string GetSystemFontPath(string fontName)
        {
            try
            {
                // مسارات الخطوط في Windows
                string[] fontPaths = {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts)),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts")
                };

                // قائمة أسماء ملفات الخطوط المحتملة
                string[] possibleFileNames = GetPossibleFontFileNames(fontName);

                foreach (string fontPath in fontPaths)
                {
                    if (Directory.Exists(fontPath))
                    {
                        foreach (string fileName in possibleFileNames)
                        {
                            string fullPath = Path.Combine(fontPath, fileName);
                            if (File.Exists(fullPath))
                                return fullPath;
                        }
                    }
                }

                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private string[] GetPossibleFontFileNames(string fontName)
        {
            // تحويل اسم الخط إلى أسماء ملفات محتملة
            switch (fontName.ToLower())
            {
                case "tahoma":
                    return new[] { "tahoma.ttf", "tahomabd.ttf" };
                case "arial":
                    return new[] { "arial.ttf", "arialbd.ttf", "ariali.ttf", "arialbi.ttf" };
                case "segoe ui":
                    return new[] { "segoeui.ttf", "segoeuib.ttf", "segoeuii.ttf", "segoeuiz.ttf" };
                case "calibri":
                    return new[] { "calibri.ttf", "calibrib.ttf", "calibrii.ttf", "calibriz.ttf" };
                case "times new roman":
                    return new[] { "times.ttf", "timesbd.ttf", "timesi.ttf", "timesbi.ttf" };
                default:
                    return new[] { $"{fontName.ToLower().Replace(" ", "")}.ttf" };
            }
        }

        private byte[] GetDefaultFont()
        {
            try
            {
                // محاولة الحصول على Arial كخط افتراضي
                string arialPath = GetSystemFontPath("Arial");
                if (!string.IsNullOrEmpty(arialPath) && File.Exists(arialPath))
                {
                    return File.ReadAllBytes(arialPath);
                }

                // إذا لم يتم العثور على Arial، محاولة Segoe UI
                string segoeUIPath = GetSystemFontPath("Segoe UI");
                if (!string.IsNullOrEmpty(segoeUIPath) && File.Exists(segoeUIPath))
                {
                    return File.ReadAllBytes(segoeUIPath);
                }

                // إذا لم يتم العثور على أي خط، إرجاع مصفوفة فارغة
                return new byte[0];
            }
            catch
            {
                return new byte[0];
            }
        }
    }
}
