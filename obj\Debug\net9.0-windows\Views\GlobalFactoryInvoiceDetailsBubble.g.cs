﻿#pragma checksum "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4530CE3652CD81C1B48DB76C52494EE3B66D5285"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// GlobalFactoryInvoiceDetailsBubble
    /// </summary>
    public partial class GlobalFactoryInvoiceDetailsBubble : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 75 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PersonNameTitle;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceIdText;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PaymentStatusBorder;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentStatusText;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QuantityText;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnitPriceText;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceDateText;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceTypeText;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateText;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountText;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentPercentageText;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PaymentProgressBar;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionText;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AttachmentSection;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AttachmentNameText;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenAttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PartialPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditInvoiceButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/globalfactoryinvoicedetailsbubble.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 76 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PersonNameTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.InvoiceIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.PaymentStatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.PaymentStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.QuantityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.UnitPriceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.InvoiceDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.InvoiceTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CreatedDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.RemainingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.PaymentPercentageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.PaymentProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 16:
            this.DescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.AttachmentSection = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.AttachmentNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.OpenAttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 316 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
            this.OpenAttachmentButton.Click += new System.Windows.RoutedEventHandler(this.OpenAttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PartialPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 340 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
            this.PartialPaymentButton.Click += new System.Windows.RoutedEventHandler(this.PartialPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.EditInvoiceButton = ((System.Windows.Controls.Button)(target));
            
            #line 354 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
            this.EditInvoiceButton.Click += new System.Windows.RoutedEventHandler(this.EditInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 367 "..\..\..\..\Views\GlobalFactoryInvoiceDetailsBubble.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

