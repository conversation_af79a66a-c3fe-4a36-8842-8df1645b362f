using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DebtManagementApp.Models;

namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// مولد PDF باستخدام QuestPDF مع دعم اللغة العربية
    /// </summary>
    public static class QuestPdfGenerator
    {
        static QuestPdfGenerator()
        {
            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;

            // تهيئة الخطوط العربية
            ArabicFontManager.Initialize();
        }

        /// <summary>
        /// إنشاء تقرير ديون شخص باستخدام QuestPDF
        /// </summary>
        public static void GeneratePersonDebtsReport(string filePath, string personName, List<Debt> debts, Models.Person? person = null)
        {
            // الحصول على الخط المفضل للعربية
            var arabicFont = ArabicFontManager.GetPreferredArabicFont();

            // الحصول على معلومات الشركة
            var companyInfo = GetCompanyInfo();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(1.5f, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(11).FontFamily(arabicFont).DirectionFromRightToLeft());

                    page.Header()
                        .Height(140, Unit.Point)
                        .Background(Colors.Grey.Lighten4)
                        .Padding(15)
                        .Column(header =>
                        {
                            // اللوجو ومعلومات الشركة
                            header.Item().Row(row =>
                            {
                                // اللوجو على اليسار
                                row.ConstantItem(80).Column(logoColumn =>
                                {
                                    if (companyInfo.HasLogo)
                                    {
                                        logoColumn.Item().Height(60).Width(60).Image(companyInfo.LogoPath);
                                    }
                                });

                                // معلومات الشركة في الوسط
                                row.RelativeItem().Column(companyColumn =>
                                {
                                    if (!string.IsNullOrEmpty(companyInfo.Name))
                                    {
                                        companyColumn.Item().Text(companyInfo.Name)
                                            .FontSize(16).SemiBold().FontColor(Colors.Blue.Darken2)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft().AlignCenter();
                                    }

                                    if (!string.IsNullOrEmpty(companyInfo.Address))
                                    {
                                        companyColumn.Item().Text(companyInfo.Address)
                                            .FontSize(10).FontColor(Colors.Grey.Darken1)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft().AlignCenter();
                                    }

                                    if (!string.IsNullOrEmpty(companyInfo.Phone))
                                    {
                                        companyColumn.Item().Text($"هاتف: {companyInfo.Phone}")
                                            .FontSize(10).FontColor(Colors.Grey.Darken1)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft().AlignCenter();
                                    }
                                });

                                // مساحة فارغة على اليمين للتوازن
                                row.ConstantItem(80);
                            });

                            // عنوان التقرير
                            header.Item().PaddingTop(10).Text($"تقرير ديون {personName}")
                                .FontSize(20).SemiBold().FontColor(Colors.Blue.Darken3)
                                .FontFamily(arabicFont).DirectionFromRightToLeft().AlignCenter();
                        });

                    page.Content()
                        .PaddingVertical(0.5f, Unit.Centimetre)
                        .Column(column =>
                        {
                            column.Spacing(15);

                            // معلومات الشخص في صندوق
                            column.Item().ShowEntire().Background(Colors.Blue.Lighten4).Padding(12).Column(personInfo =>
                            {
                                personInfo.Item().Text("معلومات العميل").FontSize(14).SemiBold()
                                    .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).DirectionFromRightToLeft();

                                personInfo.Item().PaddingTop(5).Row(row =>
                                {
                                    row.RelativeItem().Text($"الاسم: {personName}").FontSize(12).SemiBold()
                                        .FontFamily(arabicFont).DirectionFromRightToLeft();

                                    if (person != null && !string.IsNullOrEmpty(person.Phone))
                                    {
                                        row.RelativeItem().Text($"الهاتف: {person.Phone}").FontSize(12)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                    }

                                    row.RelativeItem().Text($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}").FontSize(12)
                                        .FontFamily(arabicFont).DirectionFromRightToLeft();
                                });
                            });

                            // إحصائيات الديون
                            column.Item().ShowEntire().Background(Colors.Green.Lighten4).Padding(15).Column(stats =>
                            {
                                stats.Item().Text("ملخص الديون").FontSize(16).SemiBold()
                                    .FontColor(Colors.Green.Darken2).FontFamily(arabicFont).DirectionFromRightToLeft();

                                var totalAmount = debts.Sum(d => d.Amount);
                                var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
                                var pendingAmount = totalAmount - settledAmount;
                                var settledCount = debts.Count(d => d.IsSettled);
                                var pendingCount = debts.Count(d => !d.IsSettled);

                                stats.Item().PaddingTop(8).Row(row =>
                                {
                                    row.RelativeItem().Column(col =>
                                    {
                                        col.Item().Text($"إجمالي الديون: {debts.Count} دين").FontSize(12).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        col.Item().Text($"إجمالي المبلغ: {totalAmount:N0} دينار عراقي").FontSize(12).SemiBold()
                                            .FontColor(Colors.Blue.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();
                                    });

                                    row.RelativeItem().Column(col =>
                                    {
                                        col.Item().Text($"الديون المسددة: {settledCount} دين").FontSize(12)
                                            .FontColor(Colors.Green.Darken2).FontFamily(arabicFont).DirectionFromRightToLeft();
                                        col.Item().Text($"المبلغ المسدد: {settledAmount:N0} د.ع").FontSize(12)
                                            .FontColor(Colors.Green.Darken2).FontFamily(arabicFont).DirectionFromRightToLeft();
                                    });

                                    row.RelativeItem().Column(col =>
                                    {
                                        col.Item().Text($"الديون المتبقية: {pendingCount} دين").FontSize(12)
                                            .FontColor(Colors.Red.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();
                                        col.Item().Text($"المبلغ المتبقي: {pendingAmount:N0} د.ع").FontSize(12)
                                            .FontColor(Colors.Red.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();
                                    });
                                });
                            });

                            // جدول الديون
                            if (debts.Any())
                            {
                                column.Item().Text("تفاصيل الديون").FontSize(16).SemiBold()
                                    .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).DirectionFromRightToLeft();

                                column.Item().Table(table =>
                                {
                                    // تعريف الأعمدة بأحجام محسنة
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(1.5f); // المبلغ
                                        columns.RelativeColumn(0.8f); // العدد
                                        columns.RelativeColumn(1.2f); // سعر المفرد
                                        columns.RelativeColumn(1.2f); // تاريخ الدين
                                        columns.RelativeColumn(1.2f); // تاريخ الاستحقاق
                                        columns.RelativeColumn(1.5f); // نوع العملية
                                        columns.RelativeColumn(2f); // الوصف
                                        columns.RelativeColumn(1.5f); // الملاحظات
                                        columns.RelativeColumn(1f); // الحالة
                                    });

                                    // رأس الجدول
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("العدد").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("سعر المفرد").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("تاريخ الدين").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("تاريخ الاستحقاق").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("نوع العملية").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("الوصف").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("الملاحظات").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(HeaderCellStyle).Text("الحالة").FontSize(11).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();

                                        static IContainer HeaderCellStyle(IContainer container)
                                        {
                                            return container.Background(Colors.Blue.Darken2).PaddingVertical(8).PaddingHorizontal(4)
                                                .DefaultTextStyle(x => x.FontColor(Colors.White).SemiBold());
                                        }
                                    });

                                    // بيانات الجدول
                                    var rowIndex = 0;
                                    foreach (var debt in debts.OrderByDescending(d => d.Date).Take(30))
                                    {
                                        var isEvenRow = rowIndex % 2 == 0;

                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Amount:N0}").FontSize(10).FontFamily(arabicFont).SemiBold();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Quantity?.ToString("N0") ?? "1"}").FontSize(10).FontFamily(arabicFont);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.UnitPrice?.ToString("N0") ?? "0"}").FontSize(10).FontFamily(arabicFont);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Date:yyyy/MM/dd}").FontSize(10).FontFamily(arabicFont);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.DueDate:yyyy/MM/dd}").FontSize(10).FontFamily(arabicFont);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.OperationType ?? "غير محدد", 15)).FontSize(10)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.Description ?? "", 25)).FontSize(10)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.Notes ?? "", 20)).FontSize(10)
                                            .FontFamily(arabicFont).DirectionFromRightToLeft();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(debt.IsSettled ? "مسدد" : "غير مسدد")
                                            .FontSize(10).SemiBold()
                                            .FontFamily(arabicFont).DirectionFromRightToLeft()
                                            .FontColor(debt.IsSettled ? Colors.Green.Darken2 : Colors.Red.Darken2);

                                        rowIndex++;
                                    }

                                    static IContainer DataCellStyle(IContainer container, bool isEvenRow)
                                    {
                                        return container
                                            .Background(isEvenRow ? Colors.Grey.Lighten5 : Colors.White)
                                            .BorderBottom(1).BorderColor(Colors.Grey.Lighten3)
                                            .PaddingVertical(6).PaddingHorizontal(4);
                                    }
                                });
                            }
                        });

                    page.Footer()
                        .Height(60, Unit.Point)
                        .Background(Colors.Grey.Lighten4)
                        .Padding(10)
                        .Row(footer =>
                        {
                            footer.RelativeItem().Column(leftColumn =>
                            {
                                leftColumn.Item().Text($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd}")
                                    .FontSize(9).FontColor(Colors.Grey.Darken2)
                                    .FontFamily(arabicFont).DirectionFromRightToLeft();
                                leftColumn.Item().Text($"وقت الطباعة: {DateTime.Now:HH:mm}")
                                    .FontSize(9).FontColor(Colors.Grey.Darken2)
                                    .FontFamily(arabicFont).DirectionFromRightToLeft();
                            });

                            footer.RelativeItem().Column(centerColumn =>
                            {
                                centerColumn.Item().Text("نظام إدارة الديون")
                                    .FontSize(11).SemiBold().FontColor(Colors.Blue.Darken2)
                                    .FontFamily(arabicFont).DirectionFromRightToLeft().AlignCenter();
                                centerColumn.Item().Text("تم إنشاؤه بواسطة نظام إدارة الديون المتقدم")
                                    .FontSize(8).FontColor(Colors.Grey.Darken1)
                                    .FontFamily(arabicFont).DirectionFromRightToLeft().AlignCenter();
                            });

                            footer.RelativeItem().Column(rightColumn =>
                            {
                                rightColumn.Item().Text("صفحة رقم: 1")
                                    .FontSize(9).FontColor(Colors.Grey.Darken2)
                                    .FontFamily(arabicFont).DirectionFromRightToLeft().AlignRight();
                            });
                        });
                });
            });

            document.GeneratePdf(filePath);
        }

        /// <summary>
        /// اقتطاع النص إلى طول محدد
        /// </summary>
        private static string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// الحصول على معلومات الشركة
        /// </summary>
        private static CompanyInfo GetCompanyInfo()
        {
            try
            {
                var logoPath = DebtManagementApp.Properties.Settings.Default.CompanyLogoPath;
                var hasLogo = !string.IsNullOrEmpty(logoPath) && File.Exists(logoPath);

                return new CompanyInfo
                {
                    Name = DebtManagementApp.Properties.Settings.Default.CompanyName,
                    Address = DebtManagementApp.Properties.Settings.Default.CompanyAddress,
                    Phone = DebtManagementApp.Properties.Settings.Default.CompanyPhone,
                    Email = DebtManagementApp.Properties.Settings.Default.CompanyEmail,
                    LogoPath = hasLogo ? logoPath : string.Empty,
                    HasLogo = hasLogo
                };
            }
            catch
            {
                return new CompanyInfo
                {
                    Name = "شركة الحديد والصلب المتقدمة",
                    Address = "بغداد - العراق",
                    Phone = "07901234567",
                    Email = "<EMAIL>",
                    LogoPath = string.Empty,
                    HasLogo = false
                };
            }
        }

        /// <summary>
        /// معلومات الشركة
        /// </summary>
        private class CompanyInfo
        {
            public string Name { get; set; } = string.Empty;
            public string Address { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string LogoPath { get; set; } = string.Empty;
            public bool HasLogo { get; set; }
        }
    }
}
