using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DebtManagementApp.Models;

namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// مولد PDF باستخدام QuestPDF مع دعم اللغة العربية
    /// </summary>
    public static class QuestPdfGenerator
    {
        static QuestPdfGenerator()
        {
            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;

            // تهيئة الخطوط العربية
            ArabicFontManager.Initialize();
        }

        /// <summary>
        /// إنشاء تقرير ديون شخص باستخدام QuestPDF
        /// </summary>
        public static void GeneratePersonDebtsReport(string filePath, string personName, List<Debt> debts, Models.Person? person = null)
        {
            // الحصول على الخط المفضل للعربية
            var arabicFont = ArabicFontManager.GetPreferredArabicFont();

            // الحصول على معلومات الشركة
            var companyInfo = GetCompanyInfo();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(1.5f, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(11).FontFamily(arabicFont));

                    page.Header()
                        .Height(100, Unit.Point)
                        .Background(Colors.Grey.Lighten4)
                        .Padding(12)
                        .Row(header =>
                        {
                            // معلومات الشركة على اليمين
                            header.RelativeItem().Column(companyColumn =>
                            {
                                if (!string.IsNullOrEmpty(companyInfo.Name))
                                {
                                    companyColumn.Item().Text(companyInfo.Name)
                                        .FontSize(16).SemiBold().FontColor(Colors.Blue.Darken2)
                                        .FontFamily(arabicFont).AlignRight();
                                }

                                if (!string.IsNullOrEmpty(companyInfo.Address))
                                {
                                    companyColumn.Item().Text(companyInfo.Address)
                                        .FontSize(10).FontColor(Colors.Grey.Darken1)
                                        .FontFamily(arabicFont).AlignRight();
                                }

                                if (!string.IsNullOrEmpty(companyInfo.Phone))
                                {
                                    companyColumn.Item().Text($"هاتف: {companyInfo.Phone}")
                                        .FontSize(10).FontColor(Colors.Grey.Darken1)
                                        .FontFamily(arabicFont).AlignRight();
                                }
                            });

                            // اللوجو على اليسار
                            header.ConstantItem(80).Column(logoColumn =>
                            {
                                if (companyInfo.HasLogo)
                                {
                                    logoColumn.Item().Height(60).Width(60).Image(companyInfo.LogoPath);
                                }
                            });
                        });

                    page.Content()
                        .PaddingVertical(0.5f, Unit.Centimetre)
                        .Column(column =>
                        {
                            column.Spacing(15);

                            // معلومات الشخص في صندوق مصغر
                            column.Item().ShowEntire().Background(Colors.Blue.Lighten4).Padding(8).Row(personInfo =>
                            {
                                personInfo.RelativeItem().Text($"العميل: {personName}").FontSize(12).SemiBold()
                                    .FontFamily(arabicFont).AlignRight();

                                if (person != null && !string.IsNullOrEmpty(person.Phone))
                                {
                                    personInfo.RelativeItem().Text($"الهاتف: {person.Phone}").FontSize(11)
                                        .FontFamily(arabicFont).AlignCenter();
                                }

                                personInfo.RelativeItem().Text($"التاريخ: {DateTime.Now:yyyy/MM/dd}").FontSize(11)
                                    .FontFamily(arabicFont).AlignLeft();
                            });

                            // إحصائيات الديون مصغرة
                            column.Item().ShowEntire().Background(Colors.Green.Lighten4).Padding(8).Row(stats =>
                            {
                                var totalAmount = debts.Sum(d => d.Amount);
                                var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
                                var pendingAmount = totalAmount - settledAmount;
                                var settledCount = debts.Count(d => d.IsSettled);
                                var pendingCount = debts.Count(d => !d.IsSettled);

                                stats.RelativeItem().Text($"إجمالي: {debts.Count} دين ({totalAmount:N0} د.ع)")
                                    .FontSize(11).SemiBold().FontFamily(arabicFont).AlignRight();

                                stats.RelativeItem().Text($"مسدد: {settledCount} دين ({settledAmount:N0} د.ع)")
                                    .FontSize(11).FontColor(Colors.Green.Darken2).FontFamily(arabicFont).AlignCenter();

                                stats.RelativeItem().Text($"متبقي: {pendingCount} دين ({pendingAmount:N0} د.ع)")
                                    .FontSize(11).FontColor(Colors.Red.Darken1).FontFamily(arabicFont).AlignLeft();
                            });

                            // جدول الديون
                            if (debts.Any())
                            {
                                column.Item().Text("تفاصيل الديون").FontSize(14).SemiBold()
                                    .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).AlignRight();

                                column.Item().Table(table =>
                                {
                                    // تعريف الأعمدة مع إضافة أعمدة جديدة
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(1.2f); // المبلغ الإجمالي
                                        columns.RelativeColumn(1.2f); // المبلغ المسدد
                                        columns.RelativeColumn(1.2f); // المبلغ المتبقي
                                        columns.RelativeColumn(0.7f); // العدد
                                        columns.RelativeColumn(1f); // سعر المفرد
                                        columns.RelativeColumn(1f); // تاريخ الدين
                                        columns.RelativeColumn(1f); // تاريخ الاستحقاق
                                        columns.RelativeColumn(1.2f); // نوع العملية
                                        columns.RelativeColumn(1.5f); // الوصف
                                        columns.RelativeColumn(1.2f); // الملاحظات
                                        columns.RelativeColumn(0.8f); // الحالة
                                    });

                                    // رأس الجدول
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ الإجمالي").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ المسدد").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ المتبقي").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("العدد").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("سعر المفرد").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("تاريخ الدين").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("تاريخ الاستحقاق").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("نوع العملية").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("الوصف").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("الملاحظات").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("الحالة").FontSize(9).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();

                                        static IContainer HeaderCellStyle(IContainer container)
                                        {
                                            return container.Background(Colors.Blue.Darken2).PaddingVertical(6).PaddingHorizontal(2)
                                                .DefaultTextStyle(x => x.FontColor(Colors.White).SemiBold());
                                        }
                                    });

                                    // بيانات الجدول
                                    var rowIndex = 0;
                                    foreach (var debt in debts.OrderByDescending(d => d.Date).Take(40))
                                    {
                                        var isEvenRow = rowIndex % 2 == 0;

                                        // حساب المبالغ
                                        var totalAmount = debt.Amount;
                                        var paidAmount = debt.PaidAmount;
                                        var remainingAmount = totalAmount - paidAmount;

                                        // تحديد نوع العملية مع التسديد الجزئي
                                        var operationType = debt.OperationType ?? "غير محدد";
                                        if (paidAmount > 0 && paidAmount < totalAmount)
                                        {
                                            operationType = "تسديد جزئي";
                                        }

                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{totalAmount:N0}").FontSize(8).FontFamily(arabicFont).SemiBold().AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{paidAmount:N0}").FontSize(8).FontFamily(arabicFont).AlignCenter()
                                            .FontColor(paidAmount > 0 ? Colors.Green.Darken2 : Colors.Grey.Darken1);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{remainingAmount:N0}").FontSize(8).FontFamily(arabicFont).AlignCenter()
                                            .FontColor(remainingAmount > 0 ? Colors.Red.Darken2 : Colors.Green.Darken2);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Quantity?.ToString("N0") ?? "1"}").FontSize(8).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.UnitPrice?.ToString("N0") ?? "0"}").FontSize(8).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Date:MM/dd}").FontSize(8).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.DueDate:MM/dd}").FontSize(8).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(operationType, 12)).FontSize(8)
                                            .FontFamily(arabicFont).AlignRight();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.Description ?? "", 18)).FontSize(8)
                                            .FontFamily(arabicFont).AlignRight();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.Notes ?? "", 15)).FontSize(8)
                                            .FontFamily(arabicFont).AlignRight();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(debt.IsSettled ? "مسدد" : "غير مسدد")
                                            .FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter()
                                            .FontColor(debt.IsSettled ? Colors.Green.Darken2 : Colors.Red.Darken2);

                                        rowIndex++;
                                    }

                                    static IContainer DataCellStyle(IContainer container, bool isEvenRow)
                                    {
                                        return container
                                            .Background(isEvenRow ? Colors.Grey.Lighten5 : Colors.White)
                                            .BorderBottom(1).BorderColor(Colors.Grey.Lighten3)
                                            .PaddingVertical(3).PaddingHorizontal(2);
                                    }
                                });
                            }
                        });

                    page.Footer()
                        .Height(30, Unit.Point)
                        .Background(Colors.Grey.Lighten4)
                        .Padding(5)
                        .Row(footer =>
                        {
                            footer.RelativeItem().Text($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}")
                                .FontSize(8).FontColor(Colors.Grey.Darken2)
                                .FontFamily(arabicFont).AlignRight();

                            footer.RelativeItem().Text("نظام إدارة الديون")
                                .FontSize(9).SemiBold().FontColor(Colors.Blue.Darken2)
                                .FontFamily(arabicFont).AlignCenter();

                            footer.RelativeItem().Text("صفحة رقم: 1")
                                .FontSize(8).FontColor(Colors.Grey.Darken2)
                                .FontFamily(arabicFont).AlignLeft();
                        });
                });
            });

            document.GeneratePdf(filePath);
        }

        /// <summary>
        /// اقتطاع النص إلى طول محدد
        /// </summary>
        private static string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// الحصول على معلومات الشركة
        /// </summary>
        private static CompanyInfo GetCompanyInfo()
        {
            try
            {
                var logoPath = DebtManagementApp.Properties.Settings.Default.CompanyLogoPath;
                var hasLogo = !string.IsNullOrEmpty(logoPath) && File.Exists(logoPath);

                return new CompanyInfo
                {
                    Name = DebtManagementApp.Properties.Settings.Default.CompanyName,
                    Address = DebtManagementApp.Properties.Settings.Default.CompanyAddress,
                    Phone = DebtManagementApp.Properties.Settings.Default.CompanyPhone,
                    Email = DebtManagementApp.Properties.Settings.Default.CompanyEmail,
                    LogoPath = hasLogo ? logoPath : string.Empty,
                    HasLogo = hasLogo
                };
            }
            catch
            {
                return new CompanyInfo
                {
                    Name = "شركة الحديد والصلب المتقدمة",
                    Address = "بغداد - العراق",
                    Phone = "07901234567",
                    Email = "<EMAIL>",
                    LogoPath = string.Empty,
                    HasLogo = false
                };
            }
        }

        /// <summary>
        /// معلومات الشركة
        /// </summary>
        private class CompanyInfo
        {
            public string Name { get; set; } = string.Empty;
            public string Address { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string LogoPath { get; set; } = string.Empty;
            public bool HasLogo { get; set; }
        }
    }
}
