using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DebtManagementApp.Models;

namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// مولد PDF باستخدام QuestPDF مع دعم اللغة العربية
    /// </summary>
    public static class QuestPdfGenerator
    {
        static QuestPdfGenerator()
        {
            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;

            // تهيئة الخطوط العربية
            ArabicFontManager.Initialize();
        }

        /// <summary>
        /// إنشاء تقرير ديون شخص باستخدام QuestPDF
        /// </summary>
        public static void GeneratePersonDebtsReport(string filePath, string personName, List<Debt> debts, Models.Person? person = null)
        {
            // الحصول على الخط المفضل للعربية
            var arabicFont = ArabicFontManager.GetPreferredArabicFont();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(12).FontFamily(arabicFont).DirectionFromRightToLeft());

                    page.Header()
                        .Height(100, Unit.Point)
                        .Background(Colors.Grey.Lighten3)
                        .Padding(20)
                        .Text(text =>
                        {
                            text.DefaultTextStyle(x => x.FontSize(20).SemiBold().FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).DirectionFromRightToLeft());
                            text.AlignCenter();
                            text.Span($"تقرير ديون {personName}").FontSize(24).SemiBold();
                        });

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            column.Spacing(20);

                            // معلومات الشخص
                            column.Item().ShowEntire().Column(personInfo =>
                            {
                                personInfo.Item().Text($"الاسم: {personName}").FontSize(16).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                if (person != null && !string.IsNullOrEmpty(person.Phone))
                                {
                                    personInfo.Item().Text($"الهاتف: {person.Phone}").FontSize(14).FontFamily(arabicFont).DirectionFromRightToLeft();
                                }
                                personInfo.Item().Text($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}").FontSize(14).FontFamily(arabicFont).DirectionFromRightToLeft();
                            });

                            // إحصائيات الديون
                            column.Item().ShowEntire().Background(Colors.Grey.Lighten4).Padding(15).Column(stats =>
                            {
                                stats.Item().Text("ملخص الديون").FontSize(18).SemiBold().FontColor(Colors.Blue.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();

                                var totalAmount = debts.Sum(d => d.Amount);
                                var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
                                var pendingAmount = totalAmount - settledAmount;
                                var settledCount = debts.Count(d => d.IsSettled);
                                var pendingCount = debts.Count(d => !d.IsSettled);

                                stats.Item().Text($"إجمالي الديون: {debts.Count} دين").FontSize(14).FontFamily(arabicFont).DirectionFromRightToLeft();
                                stats.Item().Text($"إجمالي المبلغ: {totalAmount:N0} دينار عراقي").FontSize(14).FontFamily(arabicFont).DirectionFromRightToLeft();
                                stats.Item().Text($"الديون المسددة: {settledCount} دين ({settledAmount:N0} د.ع)").FontSize(14).FontColor(Colors.Green.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();
                                stats.Item().Text($"الديون المتبقية: {pendingCount} دين ({pendingAmount:N0} د.ع)").FontSize(14).FontColor(Colors.Red.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();
                            });

                            // جدول الديون
                            if (debts.Any())
                            {
                                column.Item().Text("تفاصيل الديون").FontSize(18).SemiBold().FontColor(Colors.Blue.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft();
                                
                                column.Item().Table(table =>
                                {
                                    // تعريف الأعمدة
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(2); // المبلغ
                                        columns.RelativeColumn(1); // العدد
                                        columns.RelativeColumn(2); // سعر المفرد
                                        columns.RelativeColumn(2); // تاريخ الدين
                                        columns.RelativeColumn(2); // تاريخ الاستحقاق
                                        columns.RelativeColumn(2); // نوع العملية
                                        columns.RelativeColumn(3); // الوصف
                                        columns.RelativeColumn(2); // الملاحظات
                                        columns.RelativeColumn(2); // الحالة
                                    });

                                    // رأس الجدول
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(CellStyle).Text("المبلغ").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("العدد").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("سعر المفرد").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("تاريخ الدين").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("تاريخ الاستحقاق").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("نوع العملية").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("الوصف").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("الملاحظات").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();
                                        header.Cell().Element(CellStyle).Text("الحالة").FontSize(10).SemiBold().FontFamily(arabicFont).DirectionFromRightToLeft();

                                        static IContainer CellStyle(IContainer container)
                                        {
                                            return container.DefaultTextStyle(x => x.SemiBold()).PaddingVertical(5).BorderBottom(1).BorderColor(Colors.Black);
                                        }
                                    });

                                    // بيانات الجدول
                                    foreach (var debt in debts.OrderByDescending(d => d.Date).Take(25))
                                    {
                                        table.Cell().Element(CellStyle).Text($"{debt.Amount:N0}").FontSize(9).FontFamily(arabicFont);
                                        table.Cell().Element(CellStyle).Text($"{debt.Quantity?.ToString("N0") ?? "1"}").FontSize(9).FontFamily(arabicFont);
                                        table.Cell().Element(CellStyle).Text($"{debt.UnitPrice?.ToString("N0") ?? "0"}").FontSize(9).FontFamily(arabicFont);
                                        table.Cell().Element(CellStyle).Text($"{debt.Date:yyyy/MM/dd}").FontSize(9).FontFamily(arabicFont);
                                        table.Cell().Element(CellStyle).Text($"{debt.DueDate:yyyy/MM/dd}").FontSize(9).FontFamily(arabicFont);
                                        table.Cell().Element(CellStyle).Text(TruncateText(debt.OperationType ?? "غير محدد", 12)).FontSize(9).FontFamily(arabicFont).DirectionFromRightToLeft();
                                        table.Cell().Element(CellStyle).Text(TruncateText(debt.Description ?? "", 20)).FontSize(9).FontFamily(arabicFont).DirectionFromRightToLeft();
                                        table.Cell().Element(CellStyle).Text(TruncateText(debt.Notes ?? "", 15)).FontSize(9).FontFamily(arabicFont).DirectionFromRightToLeft();
                                        table.Cell().Element(CellStyle).Text(debt.IsSettled ? "مسدد" : "غير مسدد")
                                            .FontSize(9)
                                            .FontFamily(arabicFont)
                                            .DirectionFromRightToLeft()
                                            .FontColor(debt.IsSettled ? Colors.Green.Darken1 : Colors.Red.Darken1);

                                        static IContainer CellStyle(IContainer container)
                                        {
                                            return container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
                                        }
                                    }
                                });
                            }
                        });

                    page.Footer()
                        .Height(50, Unit.Point)
                        .Padding(10)
                        .Text(text =>
                        {
                            text.DefaultTextStyle(x => x.FontSize(10).FontColor(Colors.Grey.Darken1).FontFamily(arabicFont).DirectionFromRightToLeft());
                            text.AlignCenter();
                            text.Span($"نظام إدارة الديون - {DateTime.Now:yyyy/MM/dd HH:mm}");
                        });
                });
            });

            document.GeneratePdf(filePath);
        }

        /// <summary>
        /// اقتطاع النص إلى طول محدد
        /// </summary>
        private static string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength - 3) + "...";
        }
    }
}
