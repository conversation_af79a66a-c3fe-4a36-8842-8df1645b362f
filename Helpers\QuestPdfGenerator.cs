using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using DebtManagementApp.Models;

namespace DebtManagementApp.Helpers
{
    /// <summary>
    /// مولد PDF باستخدام QuestPDF مع دعم اللغة العربية
    /// </summary>
    public static class QuestPdfGenerator
    {
        static QuestPdfGenerator()
        {
            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;

            // تهيئة الخطوط العربية
            ArabicFontManager.Initialize();
        }

        /// <summary>
        /// إنشاء تقرير ديون شخص باستخدام QuestPDF
        /// </summary>
        public static void GeneratePersonDebtsReport(string filePath, string personName, List<Debt> debts, Models.Person? person = null)
        {
            // الحصول على الخط المفضل للعربية
            var arabicFont = ArabicFontManager.GetPreferredArabicFont();

            // الحصول على معلومات الشركة
            var companyInfo = GetCompanyInfo();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(1.5f, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(11).FontFamily(arabicFont));

                    page.Header()
                        .Height(110, Unit.Point)
                        .Background(Colors.Blue.Lighten5)
                        .BorderBottom(2).BorderColor(Colors.Blue.Darken1)
                        .Padding(15)
                        .Row(header =>
                        {
                            // اللوجو على اليمين
                            header.ConstantItem(90).Column(logoColumn =>
                            {
                                if (companyInfo.HasLogo)
                                {
                                    logoColumn.Item().AlignRight().Height(70).Width(70)
                                        .Background(Colors.White).Padding(5)
                                        .Image(companyInfo.LogoPath);
                                }
                            });

                            // مساحة فاصلة
                            header.ConstantItem(20);

                            // معلومات الشركة على اليسار
                            header.RelativeItem().Column(companyColumn =>
                            {
                                if (!string.IsNullOrEmpty(companyInfo.Name))
                                {
                                    companyColumn.Item().Text(companyInfo.Name)
                                        .FontSize(18).SemiBold().FontColor(Colors.Blue.Darken3)
                                        .FontFamily(arabicFont).AlignLeft();
                                }

                                if (!string.IsNullOrEmpty(companyInfo.Address))
                                {
                                    companyColumn.Item().PaddingTop(5).Text(companyInfo.Address)
                                        .FontSize(11).FontColor(Colors.Grey.Darken2)
                                        .FontFamily(arabicFont).AlignLeft();
                                }

                                if (!string.IsNullOrEmpty(companyInfo.Phone))
                                {
                                    companyColumn.Item().PaddingTop(2).Text($"هاتف: {companyInfo.Phone}")
                                        .FontSize(11).FontColor(Colors.Grey.Darken2)
                                        .FontFamily(arabicFont).AlignLeft();
                                }
                            });
                        });

                    page.Content()
                        .PaddingVertical(0.5f, Unit.Centimetre)
                        .Column(column =>
                        {
                            column.Spacing(15);

                            // معلومات العميل مع تصميم عصري
                            column.Item().ShowEntire().Background(Colors.Green.Lighten5)
                                .BorderLeft(4).BorderColor(Colors.Green.Darken1)
                                .Padding(12).Row(personInfo =>
                            {
                                // اسم العميل على اليمين
                                personInfo.RelativeItem().Text($"العميل: {personName}").FontSize(13).SemiBold()
                                    .FontColor(Colors.Green.Darken2).FontFamily(arabicFont).AlignRight();

                                // الهاتف في الوسط
                                if (person != null && !string.IsNullOrEmpty(person.Phone))
                                {
                                    personInfo.RelativeItem().Text($"الهاتف: {person.Phone}").FontSize(12)
                                        .FontColor(Colors.Grey.Darken2).FontFamily(arabicFont).AlignCenter();
                                }

                                // التاريخ على اليسار
                                personInfo.RelativeItem().Text($"التاريخ: {DateTime.Now:yyyy/MM/dd}").FontSize(12)
                                    .FontColor(Colors.Grey.Darken2).FontFamily(arabicFont).AlignLeft();
                            });

                            // إحصائيات الديون مع تصميم عصري
                            column.Item().ShowEntire().Background(Colors.Orange.Lighten5)
                                .BorderLeft(4).BorderColor(Colors.Orange.Darken1)
                                .Padding(12).Row(stats =>
                            {
                                var totalAmount = debts.Sum(d => d.Amount);
                                var settledAmount = debts.Where(d => d.IsSettled).Sum(d => d.Amount);
                                var pendingAmount = totalAmount - settledAmount;
                                var settledCount = debts.Count(d => d.IsSettled);
                                var pendingCount = debts.Count(d => !d.IsSettled);

                                // إجمالي الديون على اليمين
                                stats.RelativeItem().Column(col =>
                                {
                                    col.Item().Text("إجمالي الديون").FontSize(10).SemiBold()
                                        .FontColor(Colors.Orange.Darken2).FontFamily(arabicFont).AlignRight();
                                    col.Item().Text($"{debts.Count} دين").FontSize(12).SemiBold()
                                        .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).AlignRight();
                                    col.Item().Text($"{totalAmount:N0} د.ع").FontSize(11)
                                        .FontColor(Colors.Blue.Darken1).FontFamily(arabicFont).AlignRight();
                                });

                                // الديون المسددة في الوسط
                                stats.RelativeItem().Column(col =>
                                {
                                    col.Item().Text("الديون المسددة").FontSize(10).SemiBold()
                                        .FontColor(Colors.Green.Darken2).FontFamily(arabicFont).AlignCenter();
                                    col.Item().Text($"{settledCount} دين").FontSize(12).SemiBold()
                                        .FontColor(Colors.Green.Darken2).FontFamily(arabicFont).AlignCenter();
                                    col.Item().Text($"{settledAmount:N0} د.ع").FontSize(11)
                                        .FontColor(Colors.Green.Darken1).FontFamily(arabicFont).AlignCenter();
                                });

                                // الديون المتبقية على اليسار
                                stats.RelativeItem().Column(col =>
                                {
                                    col.Item().Text("الديون المتبقية").FontSize(10).SemiBold()
                                        .FontColor(Colors.Red.Darken2).FontFamily(arabicFont).AlignLeft();
                                    col.Item().Text($"{pendingCount} دين").FontSize(12).SemiBold()
                                        .FontColor(Colors.Red.Darken2).FontFamily(arabicFont).AlignLeft();
                                    col.Item().Text($"{pendingAmount:N0} د.ع").FontSize(11)
                                        .FontColor(Colors.Red.Darken1).FontFamily(arabicFont).AlignLeft();
                                });
                            });

                            // جدول الديون
                            if (debts.Any())
                            {
                                column.Item().Text("تفاصيل الديون").FontSize(14).SemiBold()
                                    .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).AlignRight();

                                column.Item().Table(table =>
                                {
                                    // تعريف الأعمدة من اليمين إلى اليسار
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(1.2f); // المبلغ الإجمالي (يمين)
                                        columns.RelativeColumn(1.2f); // المبلغ المسدد
                                        columns.RelativeColumn(1.2f); // المبلغ المتبقي
                                        columns.RelativeColumn(0.7f); // العدد
                                        columns.RelativeColumn(1f); // سعر المفرد
                                        columns.RelativeColumn(1f); // تاريخ الدين
                                        columns.RelativeColumn(1f); // تاريخ الاستحقاق
                                        columns.RelativeColumn(1.2f); // نوع العملية
                                        columns.RelativeColumn(1.5f); // الوصف
                                        columns.RelativeColumn(1.2f); // الملاحظات
                                        columns.RelativeColumn(1f); // الحالة (يسار)
                                    });

                                    // رأس الجدول مع تصميم عصري
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ الإجمالي").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ المسدد").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("المبلغ المتبقي").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("العدد").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("سعر المفرد").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("تاريخ الدين").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("تاريخ الاستحقاق").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("نوع العملية").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("الوصف").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("الملاحظات").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();
                                        header.Cell().Element(HeaderCellStyle).Text("الحالة").FontSize(8).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter();

                                        static IContainer HeaderCellStyle(IContainer container)
                                        {
                                            return container.Background(Colors.Blue.Darken3)
                                                .BorderBottom(1).BorderColor(Colors.Blue.Darken4)
                                                .PaddingVertical(8).PaddingHorizontal(3)
                                                .DefaultTextStyle(x => x.FontColor(Colors.White).SemiBold());
                                        }
                                    });

                                    // بيانات الجدول
                                    var rowIndex = 0;
                                    foreach (var debt in debts.OrderByDescending(d => d.Date).Take(45))
                                    {
                                        var isEvenRow = rowIndex % 2 == 0;

                                        // حساب المبالغ
                                        var totalAmount = debt.Amount;
                                        var paidAmount = debt.PaidAmount;
                                        var remainingAmount = totalAmount - paidAmount;

                                        // نوع العملية يبقى كما هو
                                        var operationType = debt.OperationType ?? "غير محدد";

                                        // تحديد الحالة مع التسديد الجزئي
                                        var status = debt.IsSettled ? "مسدد" :
                                                   (paidAmount > 0 && paidAmount < totalAmount) ? "تسديد جزئي" : "غير مسدد";

                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{totalAmount:N0}").FontSize(7).FontFamily(arabicFont).SemiBold().AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{paidAmount:N0}").FontSize(7).FontFamily(arabicFont).AlignCenter()
                                            .FontColor(paidAmount > 0 ? Colors.Green.Darken2 : Colors.Grey.Darken1);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{remainingAmount:N0}").FontSize(7).FontFamily(arabicFont).AlignCenter()
                                            .FontColor(remainingAmount > 0 ? Colors.Red.Darken2 : Colors.Green.Darken2);
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Quantity?.ToString("N0") ?? "1"}").FontSize(7).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.UnitPrice?.ToString("N0") ?? "0"}").FontSize(7).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.Date:MM/dd}").FontSize(7).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text($"{debt.DueDate:MM/dd}").FontSize(7).FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(operationType, 10)).FontSize(7)
                                            .FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.Description ?? "", 15)).FontSize(7)
                                            .FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(TruncateText(debt.Notes ?? "", 12)).FontSize(7)
                                            .FontFamily(arabicFont).AlignCenter();
                                        table.Cell().Element(container => DataCellStyle(container, isEvenRow))
                                            .Text(status).FontSize(7).SemiBold()
                                            .FontFamily(arabicFont).AlignCenter()
                                            .FontColor(status == "مسدد" ? Colors.Green.Darken2 :
                                                      status == "تسديد جزئي" ? Colors.Orange.Darken2 : Colors.Red.Darken2);

                                        rowIndex++;
                                    }

                                    static IContainer DataCellStyle(IContainer container, bool isEvenRow)
                                    {
                                        return container
                                            .Background(isEvenRow ? Colors.Blue.Lighten5 : Colors.White)
                                            .BorderBottom(1).BorderColor(Colors.Blue.Lighten3)
                                            .BorderRight(1).BorderColor(Colors.Blue.Lighten4)
                                            .PaddingVertical(4).PaddingHorizontal(3);
                                    }
                                });
                            }
                        });

                    page.Footer()
                        .Height(35, Unit.Point)
                        .Background(Colors.Blue.Lighten5)
                        .BorderTop(1).BorderColor(Colors.Blue.Darken1)
                        .Padding(8)
                        .Row(footer =>
                        {
                            // تاريخ الطباعة على اليمين
                            footer.RelativeItem().Column(rightCol =>
                            {
                                rightCol.Item().Text("تاريخ الطباعة").FontSize(8).SemiBold()
                                    .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).AlignRight();
                                rightCol.Item().Text($"{DateTime.Now:yyyy/MM/dd HH:mm}").FontSize(8)
                                    .FontColor(Colors.Grey.Darken2).FontFamily(arabicFont).AlignRight();
                            });

                            // اسم النظام في الوسط
                            footer.RelativeItem().Text("نظام إدارة الديون")
                                .FontSize(10).SemiBold().FontColor(Colors.Blue.Darken3)
                                .FontFamily(arabicFont).AlignCenter();

                            // رقم الصفحة على اليسار
                            footer.RelativeItem().Column(leftCol =>
                            {
                                leftCol.Item().Text("رقم الصفحة").FontSize(8).SemiBold()
                                    .FontColor(Colors.Blue.Darken2).FontFamily(arabicFont).AlignLeft();
                                leftCol.Item().Text("1").FontSize(8)
                                    .FontColor(Colors.Grey.Darken2).FontFamily(arabicFont).AlignLeft();
                            });
                        });
                });
            });

            document.GeneratePdf(filePath);
        }

        /// <summary>
        /// اقتطاع النص إلى طول محدد
        /// </summary>
        private static string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// الحصول على معلومات الشركة
        /// </summary>
        private static CompanyInfo GetCompanyInfo()
        {
            try
            {
                var logoPath = DebtManagementApp.Properties.Settings.Default.CompanyLogoPath;
                var hasLogo = !string.IsNullOrEmpty(logoPath) && File.Exists(logoPath);

                return new CompanyInfo
                {
                    Name = DebtManagementApp.Properties.Settings.Default.CompanyName,
                    Address = DebtManagementApp.Properties.Settings.Default.CompanyAddress,
                    Phone = DebtManagementApp.Properties.Settings.Default.CompanyPhone,
                    Email = DebtManagementApp.Properties.Settings.Default.CompanyEmail,
                    LogoPath = hasLogo ? logoPath : string.Empty,
                    HasLogo = hasLogo
                };
            }
            catch
            {
                return new CompanyInfo
                {
                    Name = "شركة الحديد والصلب المتقدمة",
                    Address = "بغداد - العراق",
                    Phone = "07901234567",
                    Email = "<EMAIL>",
                    LogoPath = string.Empty,
                    HasLogo = false
                };
            }
        }

        /// <summary>
        /// معلومات الشركة
        /// </summary>
        private class CompanyInfo
        {
            public string Name { get; set; } = string.Empty;
            public string Address { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string LogoPath { get; set; } = string.Empty;
            public bool HasLogo { get; set; }
        }
    }
}
