﻿#pragma checksum "..\..\..\..\Views\BackupSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2D2B7DB1D468FAF88C846F479C4DB5742FFC3E51"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// BackupSettingsWindow
    /// </summary>
    public partial class BackupSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 109 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BackupFrequencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupFolderTextBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RetentionDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DeleteOldBackupsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CompressBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\BackupSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CompressionLevelComboBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/backupsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\BackupSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.BackupFrequencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.BackupFolderTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            
            #line 145 "..\..\..\..\Views\BackupSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseFolder_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RetentionDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DeleteOldBackupsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.CompressBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.CompressionLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            
            #line 212 "..\..\..\..\Views\BackupSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 215 "..\..\..\..\Views\BackupSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

