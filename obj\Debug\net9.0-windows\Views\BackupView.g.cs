﻿#pragma checksum "..\..\..\..\Views\BackupView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4976F8BBED00584654E36EFF6AC812CE12138046"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// BackupView
    /// </summary>
    public partial class BackupView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AutoBackupStatusText;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshStatusButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox BackupListBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BackupInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedBackupName;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedBackupDate;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\BackupView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedBackupSize;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/backupview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\BackupView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AutoBackupStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.RefreshStatusButton = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\Views\BackupView.xaml"
            this.RefreshStatusButton.Click += new System.Windows.RoutedEventHandler(this.RefreshStatus_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 53 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateBackup_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 58 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestoreBackup_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 63 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BackupSettings_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BackupListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 89 "..\..\..\..\Views\BackupView.xaml"
            this.BackupListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BackupListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 121 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshBackupList_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 127 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteBackup_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BackupInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.SelectedBackupName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SelectedBackupDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SelectedBackupSize = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 185 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestoreSelectedBackup_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 189 "..\..\..\..\Views\BackupView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportBackup_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

