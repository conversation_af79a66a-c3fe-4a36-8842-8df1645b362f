﻿#pragma checksum "..\..\..\..\Views\AddFactoryDebtControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9185C86ECC70641FE08E671920EB7DFF30C6132A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// AddFactoryDebtControl
    /// </summary>
    public partial class AddFactoryDebtControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 78 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PersonComboBox;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DebtDatePicker;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InvoiceTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsPaidCheckBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachFileButton;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AttachedFileText;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultPreviewContent;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image ImagePreview;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PdfPreviewContent;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PdfFileNameText;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FileInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/addfactorydebtcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PersonComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 80 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
            this.PersonComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PersonComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.DebtDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.InvoiceTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.IsPaidCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.AttachFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
            this.AttachFileButton.Click += new System.Windows.RoutedEventHandler(this.AttachFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.AttachedFileText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.DefaultPreviewContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.ImagePreview = ((System.Windows.Controls.Image)(target));
            return;
            case 12:
            this.PdfPreviewContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.PdfFileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.FileInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 190 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 200 "..\..\..\..\Views\AddFactoryDebtControl.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

