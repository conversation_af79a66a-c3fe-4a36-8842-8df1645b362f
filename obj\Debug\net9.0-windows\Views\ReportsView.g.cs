﻿#pragma checksum "..\..\..\..\Views\ReportsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "56DF153F2D1D8CBD2654649159EDD5262045BEE9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManagementApp.Views {
    
    
    /// <summary>
    /// ReportsView
    /// </summary>
    public partial class ReportsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 85 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportTitle;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContent;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPersonsCount;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDebtsCount;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountValue;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueDebtsCount;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ReportDataGrid;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmptyMessage;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManagementApp;component/views/reportsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ReportsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 37 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateComprehensiveReport_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 42 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateAmountReport_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 47 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateMonthlyReport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 52 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateCustomReport_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 66 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GeneratePartialPaymentReport_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 71 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ReportTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ReportContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.TotalPersonsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalDebtsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TotalAmountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.OverdueDebtsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ReportDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.EmptyMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            
            #line 184 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportToPDF_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 188 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportToExcel_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 192 "..\..\..\..\Views\ReportsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

